#!/usr/bin/env dart

import 'dart:io';
import 'package:args/args.dart';
import 'package:openapi_dart_gen/src/cli/cli_runner.dart';

/// Entry point for the OpenAPI Dart Generator CLI
Future<void> main(List<String> arguments) async {
  final parser = ArgParser()
    ..addCommand('generate')
    ..addFlag(
      'help',
      abbr: 'h',
      help: 'Show usage information',
      negatable: false,
    )
    ..addFlag(
      'version',
      abbr: 'v',
      help: 'Show version information',
      negatable: false,
    );

  // Add generate command options
  final generateCommand = parser.commands['generate']!
    ..addOption(
      'input',
      abbr: 'i',
      help: 'Path to OpenAPI specification file (JSON or YAML)',
      mandatory: true,
    )
    ..addOption(
      'output',
      abbr: 'o',
      help: 'Output directory for generated code',
      defaultsTo: 'lib/generated',
    )
    ..addOption(
      'client-name',
      help: 'Name for the generated API client class',
      defaultsTo: 'ApiClient',
    )
    ..addOption(
      'base-url',
      help: 'Base URL for the API client',
    )
    ..addFlag(
      'help',
      abbr: 'h',
      help: 'Show help for generate command',
      negatable: false,
    );

  try {
    final results = parser.parse(arguments);

    if (results['help'] as bool) {
      _showHelp(parser);
      return;
    }

    if (results['version'] as bool) {
      _showVersion();
      return;
    }

    if (results.command?.name == 'generate') {
      final generateResults = results.command!;
      
      if (generateResults['help'] as bool) {
        _showGenerateHelp(generateCommand);
        return;
      }

      final runner = CliRunner();
      await runner.generate(
        inputPath: generateResults['input'] as String,
        outputPath: generateResults['output'] as String,
        clientName: generateResults['client-name'] as String,
        baseUrl: generateResults['base-url'] as String?,
      );
    } else {
      _showHelp(parser);
    }
  } catch (e) {
    stderr.writeln('Error: $e');
    exit(1);
  }
}

void _showHelp(ArgParser parser) {
  print('OpenAPI Dart Generator - Generate Dart code from OpenAPI specifications\n');
  print('Usage: openapi_dart_gen <command> [options]\n');
  print('Commands:');
  print('  generate    Generate Dart code from OpenAPI specification\n');
  print('Global options:');
  print(parser.usage);
  print('\nFor command-specific help, use: openapi_dart_gen <command> --help');
}

void _showGenerateHelp(ArgParser generateCommand) {
  print('Generate Dart code from OpenAPI specification\n');
  print('Usage: openapi_dart_gen generate [options]\n');
  print('Options:');
  print(generateCommand.usage);
  print('\nExamples:');
  print('  openapi_dart_gen generate -i openapi.yaml -o lib/api/');
  print('  openapi_dart_gen generate --input openapi.json --output lib/generated/ --client-name MyApiClient');
}

void _showVersion() {
  print('OpenAPI Dart Generator v0.1.0');
}
