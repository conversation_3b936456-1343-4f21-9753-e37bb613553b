# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2024-01-XX

### Added

#### Core Features

- 🚀 **Complete OpenAPI 3.x Support** - Full parsing and code generation for OpenAPI 3.0+ specifications
- 🎯 **Type-Safe Code Generation** - Generate strongly-typed Dart models and API clients
- 🌐 **Dio Integration** - Built-in Dio HTTP client with comprehensive error handling
- 📦 **Model Generation** - Automatic Dart model classes with JSON serialization/deserialization
- 🔧 **API Client Generation** - Type-safe API service classes organized by OpenAPI tags
- ⚡ **CLI Tool** - Easy-to-use command-line interface for code generation

#### Model Generation

- Custom `fromJson()` and `toJson()` methods without external dependencies
- Support for complex nested objects and arrays
- Proper handling of required vs optional fields
- Nullable type support with Dart null safety
- Schema reference resolution (`$ref` support)
- Composition schema support (anyOf, oneOf, allOf)
- Enum handling with string values
- Documentation preservation from OpenAPI descriptions

#### API Client Generation

- Service classes organized by OpenAPI tags
- Support for all HTTP methods (GET, POST, PUT, DELETE, PATCH)
- Path parameter replacement with type safety
- Query parameter handling with optional parameters
- Request body serialization using generated models
- Response deserialization to typed objects
- Header parameter support
- Type-safe return types based on OpenAPI response schemas

#### Error Handling

- Comprehensive exception hierarchy for different HTTP status codes
- `ApiException` base class for all API-related errors
- `ClientException` for 4xx client errors
- `ServerException` for 5xx server errors
- `NetworkException` for connection/network issues
- `TimeoutException` for request timeouts
- Specific exceptions: `UnauthorizedException`, `ForbiddenException`, `NotFoundException`, etc.
- `ApiExceptionHandler` utility for converting Dio exceptions

#### CLI Interface

- `generate` command for code generation
- Support for JSON and YAML input files
- Configurable output directory
- Custom client name option
- Base URL override capability
- Comprehensive help and error messages

#### Code Quality

- Generated code follows Dart style guidelines
- Proper null safety implementation
- Lint-free generated code
- Comprehensive documentation comments
- Type-safe parameter handling

### Technical Implementation

#### Parser

- OpenAPI 3.x specification parsing
- JSON and YAML format support
- Schema validation and error reporting
- Reference resolution for complex schemas
- Support for nested and circular references

#### Code Generation

- Template-based code generation
- Proper Dart naming conventions (camelCase, PascalCase)
- Import management and organization
- File structure optimization
- Barrel file generation for easy imports

#### Testing

- Comprehensive unit test suite (>80% coverage)
- Integration tests for end-to-end workflows
- CLI testing with various input formats
- Generated code compilation verification
- Error handling test coverage

### Dependencies

#### Runtime Dependencies

- `dio: ^5.3.2` - HTTP client for generated API clients
- `yaml: ^3.1.2` - YAML parsing support
- `args: ^2.4.2` - CLI argument parsing

#### Development Dependencies

- `test: ^1.24.0` - Testing framework
- `lints: ^2.1.0` - Dart linting rules
- `path: ^1.8.3` - Path manipulation utilities

### Known Limitations

- OpenAPI 2.x (Swagger) support not implemented
- Custom authentication schemes require manual Dio configuration
- WebSocket operations not supported
- File upload/download operations need manual handling
- Custom OpenAPI extensions not processed
- Circular reference handling has limitations

### Breaking Changes

- N/A (Initial release)

### Performance

- Fast code generation for typical OpenAPI specifications
- Efficient parsing of large OpenAPI files
- Minimal runtime overhead in generated code
- Memory-efficient model serialization

### Security

- No sensitive data stored in generated code
- Proper input validation in CLI
- Safe file system operations
- No external network calls during generation
