# OpenAPI Dart Generator

[![Pub Version](https://img.shields.io/pub/v/openapi_dart_gen.svg)](https://pub.dev/packages/openapi_dart_gen)
[![Dart SDK Version](https://badgen.net/pub/sdk-version/openapi_dart_gen)](https://pub.dev/packages/openapi_dart_gen)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

A powerful Dart package that automatically generates type-safe Dart code from OpenAPI 3.x specifications, featuring Dio HTTP client integration, comprehensive error handling, and production-ready API clients.

## ✨ Features

- 🚀 **Complete OpenAPI 3.x Support** - Full support for OpenAPI 3.0+ specifications
- 🎯 **Type-Safe Code Generation** - Generate strongly-typed Dart models and API clients
- 🌐 **Dio Integration** - Built-in Dio HTTP client with interceptors and error handling
- 🛡️ **Comprehensive Error Handling** - Custom exception classes for different HTTP status codes
- 📦 **Model Generation** - Automatic Dart model classes with JSON serialization
- 🔧 **API Client Generation** - Type-safe API service classes organized by OpenAPI tags
- 📝 **Documentation Support** - Preserves OpenAPI documentation in generated code
- 🎨 **Dart Conventions** - Generated code follows Dart style guidelines and best practices
- ⚡ **CLI Tool** - Easy-to-use command-line interface for code generation

## 🚀 Quick Start

### Installation

Add `openapi_dart_gen` to your `pubspec.yaml`:

```yaml
dev_dependencies:
  openapi_dart_gen: ^1.0.0
```

Or install globally:

```bash
dart pub global activate openapi_dart_gen
```

### Basic Usage

#### 1. Generate Code from OpenAPI Specification

```bash
# Using the global CLI
openapi_dart_gen generate -i api_spec.json -o lib/generated

# Using dart run
dart run openapi_dart_gen generate -i api_spec.json -o lib/generated
```

#### 2. Use Generated API Client

```dart
import 'package:dio/dio.dart';
import 'lib/generated/api.dart';

void main() async {
  // Create Dio instance with configuration
  final dio = Dio(BaseOptions(
    baseUrl: 'https://api.example.com',
    connectTimeout: Duration(seconds: 5),
    receiveTimeout: Duration(seconds: 3),
  ));

  // Create API client
  final apiClient = ApiClient(dio);

  try {
    // Use type-safe API methods
    final users = await apiClient.users.getUsers();
    print('Found ${users.data?.length} users');

    // Create a new user
    final newUser = User(name: 'John Doe', email: '<EMAIL>');
    final createdUser = await apiClient.users.createUser(newUser);
    print('Created user: ${createdUser.data?.name}');

  } on ApiException catch (e) {
    // Handle API-specific errors
    print('API Error: ${e.message} (Status: ${e.statusCode})');
  } on NetworkException catch (e) {
    // Handle network errors
    print('Network Error: ${e.message}');
  }
}
```

## 📖 CLI Reference

### Generate Command

Generate Dart code from an OpenAPI specification:

```bash
openapi_dart_gen generate [options]
```

#### Options

| Option          | Short | Description                                    | Required | Default     |
| --------------- | ----- | ---------------------------------------------- | -------- | ----------- |
| `--input`       | `-i`  | Path to OpenAPI specification file (JSON/YAML) | ✅       | -           |
| `--output`      | `-o`  | Output directory for generated code            | ✅       | -           |
| `--client-name` | `-n`  | Name for the generated API client class        | ❌       | `ApiClient` |
| `--base-url`    | `-b`  | Base URL for the API (overrides spec)          | ❌       | -           |
| `--help`        | `-h`  | Show help information                          | ❌       | -           |

#### Examples

```bash
# Basic generation
openapi_dart_gen generate -i petstore.json -o lib/api

# Custom client name
openapi_dart_gen generate -i api.yaml -o lib/generated -n MyApiClient

# Override base URL
openapi_dart_gen generate -i spec.json -o lib/api -b https://staging.api.com
```

## 🏗️ Generated Code Structure

The generator creates a well-organized code structure:

```
lib/generated/
├── api.dart                 # Main export file
├── models/
│   ├── models.dart         # Models barrel file
│   ├── user.dart           # Generated model classes
│   ├── product.dart
│   └── ...
├── client/
│   ├── api_client.dart     # Main API client
│   ├── users_service.dart  # Service classes by tag
│   ├── products_service.dart
│   └── ...
└── exceptions/
    └── api_exceptions.dart # Custom exception classes
```

## 🎯 Generated Features

### Type-Safe Models

```dart
class User {
  final int id;
  final String name;
  final String? email;

  const User({
    required this.id,
    required this.name,
    this.email,
  });

  factory User.fromJson(Map<String, dynamic> json) => User(
    id: json['id'] as int,
    name: json['name'] as String,
    email: json['email'] as String?,
  );

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'email': email,
  };
}
```

### API Service Classes

```dart
class UsersService {
  final Dio _dio;
  const UsersService(this._dio);

  /// Get all users with pagination
  Future<Response<List<User>>> getUsers({
    int? page,
    int? limit,
    String? search,
  }) async {
    const path = '/users';
    final queryParameters = <String, dynamic>{};
    if (page != null) queryParameters['page'] = page;
    if (limit != null) queryParameters['limit'] = limit;
    if (search != null) queryParameters['search'] = search;

    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final dataList = (response.data as List<dynamic>)
          .map((item) => User.fromJson(item as Map<String, dynamic>))
          .toList();
      return Response<List<User>>(
        data: dataList,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }
}
```

### Error Handling

```dart
// Comprehensive exception hierarchy
try {
  final result = await apiClient.users.getUser(123);
} on UnauthorizedException catch (e) {
  // Handle 401 errors
  print('Please login: ${e.message}');
} on NotFoundException catch (e) {
  // Handle 404 errors
  print('User not found: ${e.message}');
} on ValidationException catch (e) {
  // Handle 422 validation errors
  print('Validation failed: ${e.message}');
} on ServerException catch (e) {
  // Handle 5xx server errors
  print('Server error: ${e.message}');
} on NetworkException catch (e) {
  // Handle network/connection errors
  print('Network error: ${e.message}');
}
```

## 🔧 Advanced Configuration

### Custom Dio Configuration

```dart
final dio = Dio(BaseOptions(
  baseUrl: 'https://api.example.com',
  connectTimeout: Duration(seconds: 10),
  receiveTimeout: Duration(seconds: 5),
  headers: {
    'User-Agent': 'MyApp/1.0.0',
    'Accept': 'application/json',
  },
));

// Add interceptors
dio.interceptors.add(LogInterceptor(
  requestBody: true,
  responseBody: true,
));

// Add authentication
dio.interceptors.add(InterceptorsWrapper(
  onRequest: (options, handler) {
    options.headers['Authorization'] = 'Bearer $token';
    handler.next(options);
  },
));

final apiClient = ApiClient(dio);
```

### Environment-Specific Configuration

```dart
class ApiConfig {
  static const String baseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'https://api.example.com',
  );

  static const Duration timeout = Duration(
    seconds: int.fromEnvironment('API_TIMEOUT', defaultValue: 30),
  );
}

final dio = Dio(BaseOptions(
  baseUrl: ApiConfig.baseUrl,
  connectTimeout: ApiConfig.timeout,
));
```

## 🧪 Testing Generated Code

### Unit Testing Models

```dart
import 'package:test/test.dart';
import 'package:your_app/generated/models/models.dart';

void main() {
  group('User Model Tests', () {
    test('should serialize to JSON correctly', () {
      final user = User(id: 1, name: 'John Doe', email: '<EMAIL>');
      final json = user.toJson();

      expect(json['id'], equals(1));
      expect(json['name'], equals('John Doe'));
      expect(json['email'], equals('<EMAIL>'));
    });

    test('should deserialize from JSON correctly', () {
      final json = {'id': 1, 'name': 'John Doe', 'email': '<EMAIL>'};
      final user = User.fromJson(json);

      expect(user.id, equals(1));
      expect(user.name, equals('John Doe'));
      expect(user.email, equals('<EMAIL>'));
    });
  });
}
```

### Mocking API Clients

```dart
import 'package:dio/dio.dart';
import 'package:http_mock_adapter/http_mock_adapter.dart';

void main() {
  group('API Client Tests', () {
    late Dio dio;
    late DioAdapter dioAdapter;
    late ApiClient apiClient;

    setUp(() {
      dio = Dio();
      dioAdapter = DioAdapter(dio: dio);
      apiClient = ApiClient(dio);
    });

    test('should handle successful user creation', () async {
      final userData = {'id': 1, 'name': 'John Doe'};

      dioAdapter.onPost('/users', (server) => server.reply(201, userData));

      final user = User(name: 'John Doe');
      final response = await apiClient.users.createUser(user);

      expect(response.statusCode, equals(201));
      expect(response.data?.name, equals('John Doe'));
    });
  });
}
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Import Errors

```bash
Error: Could not resolve the package 'openapi_dart_gen' in 'package:openapi_dart_gen/openapi_dart_gen.dart'.
```

**Solution:** Make sure the package is added to your `pubspec.yaml` and run `dart pub get`.

#### 2. Generation Fails

```bash
Error: Failed to parse OpenAPI specification
```

**Solution:** Validate your OpenAPI specification using online validators like [Swagger Editor](https://editor.swagger.io/).

#### 3. Type Errors in Generated Code

```bash
Error: The argument type 'String?' can't be assigned to the parameter type 'String'.
```

**Solution:** Check your OpenAPI schema for proper `required` field definitions and nullable types.

### Getting Help

- 📖 [Documentation](https://pub.dev/documentation/openapi_dart_gen)
- 🐛 [Report Issues](https://github.com/your-username/openapi_dart_gen/issues)
- 💬 [Discussions](https://github.com/your-username/openapi_dart_gen/discussions)

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

```bash
# Clone the repository
git clone https://github.com/your-username/openapi_dart_gen.git
cd openapi_dart_gen

# Install dependencies
dart pub get

# Run tests
dart test

# Run the generator
dart run bin/openapi_dart_gen.dart generate -i example.json -o test_output
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Inspired by [hey-api/openapi-ts](https://github.com/hey-api/openapi-ts)
- Built with [Dio](https://pub.dev/packages/dio) for HTTP client functionality
- Thanks to the Dart and Flutter community for their support

---

**Made with ❤️ for the Dart community**
