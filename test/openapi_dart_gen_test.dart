import 'package:openapi_dart_gen/openapi_dart_gen.dart';
import 'package:test/test.dart';

// Import all test suites
import 'parser/openapi_parser_test.dart' as parser_tests;
import 'generator/model_generator_test.dart' as model_tests;
import 'cli/cli_runner_test.dart' as cli_tests;
import 'integration/end_to_end_test.dart' as integration_tests;

void main() {
  group('OpenAPI Dart Generator Tests', () {
    setUp(() {
      // Additional setup goes here.
    });

    test('OpenAPI Parser can be instantiated', () {
      final parser = OpenApiParser();
      expect(parser, isNotNull);
    });

    test('Code Generator can be instantiated', () {
      final generator = CodeGenerator();
      expect(generator, isNotNull);
    });

    test('Model Generator can be instantiated', () {
      final generator = ModelGenerator();
      expect(generator, isNotNull);
    });

    test('Client Generator can be instantiated', () {
      final generator = ClientGenerator();
      expect(generator, isNotNull);
    });

    test('CLI Runner can be instantiated', () {
      final runner = CliRunner();
      expect(runner, isNotNull);
    });
  });

  // Run all test suites
  group('Parser Tests', parser_tests.main);
  group('Model Generator Tests', model_tests.main);
  group('CLI Tests', cli_tests.main);
  group('Integration Tests', integration_tests.main);
}
