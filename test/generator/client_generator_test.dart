import 'dart:io';
import 'package:openapi_dart_gen/openapi_dart_gen.dart';
import 'package:test/test.dart';
import 'package:path/path.dart' as path;
import 'package:openapi_dart_gen/src/models/openapi_models.dart';
import 'package:openapi_dart_gen/src/models/openapi_models_extended.dart'
    as openapi;

void main() {
  group('ClientGenerator Tests', () {
    late ClientGenerator generator;
    late Directory tempDir;

    setUp(() async {
      generator = ClientGenerator();
      tempDir = await Directory.systemTemp.createTemp('client_generator_test');
    });

    tearDown(() async {
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }
    });

    test('should generate basic API client service', () async {
      final spec = OpenApiSpec(
        openapi: '3.0.0',
        info: Info(title: 'Test API', version: '1.0.0'),
        paths: {
          '/users': PathItem(
            get: Operation(
              operationId: 'getUsers',
              tags: ['users'],
              summary: 'Get all users',
              responses: {
                '200': openapi.Response(
                  description: 'Success',
                  content: {
                    'application/json': openapi.MediaType(
                      schema: Schema(
                        type: 'array',
                        items: Schema(ref: '#/components/schemas/User'),
                      ),
                    ),
                  },
                ),
              },
            ),
          ),
        },
        components: openapi.Components(
          schemas: {
            'User': Schema(
              type: 'object',
              properties: {
                'id': Schema(type: 'integer'),
                'name': Schema(type: 'string'),
              },
            ),
          },
        ),
      );

      final config = GenerationConfig(
        outputPath: tempDir.path,
        clientName: 'TestApiClient',
      );
      await generator.generateClient(spec, config, tempDir.path);

      final usersServiceFile = File(
        path.join(tempDir.path, 'users_service.dart'),
      );
      expect(await usersServiceFile.exists(), isTrue);

      final content = await usersServiceFile.readAsString();

      // Check service class
      expect(content, contains('class UsersService {'));
      expect(content, contains('final Dio _dio;'));
      expect(content, contains('const UsersService(this._dio);'));

      // Check method generation (method names are generated in lowercase)
      expect(content, contains('Future<Response<List<User>>> getusers()'));
      expect(content, contains('const path = \'/users\';'));
      expect(content, contains('await _dio.get(path)'));

      // Check imports
      expect(content, contains("import 'package:dio/dio.dart';"));
      expect(content, contains("import '../models/models.dart';"));
      expect(
        content,
        contains("import 'package:openapi_dart_gen/openapi_dart_gen.dart';"),
      );
    });

    test('should generate client with path parameters', () async {
      final spec = OpenApiSpec(
        openapi: '3.0.0',
        info: Info(title: 'Test API', version: '1.0.0'),
        paths: {
          '/users/{id}': PathItem(
            get: Operation(
              operationId: 'getUserById',
              tags: ['users'],
              parameters: [
                Parameter(
                  name: 'id',
                  parameterIn: 'path',
                  required: true,
                  schema: Schema(type: 'integer'),
                ),
              ],
              responses: {
                '200': openapi.Response(
                  description: 'Success',
                  content: {
                    'application/json': openapi.MediaType(
                      schema: Schema(ref: '#/components/schemas/User'),
                    ),
                  },
                ),
              },
            ),
          ),
        },
        components: openapi.Components(
          schemas: {'User': Schema(type: 'object')},
        ),
      );

      final config = GenerationConfig(
        outputPath: tempDir.path,
        clientName: 'TestApiClient',
      );
      await generator.generateClient(spec, config, tempDir.path);

      final usersServiceFile = File(
        path.join(tempDir.path, 'users_service.dart'),
      );
      final content = await usersServiceFile.readAsString();

      // Check path parameter handling (method names are generated in lowercase)
      expect(content, contains('Future<Response<User>> getuserbyid(int id)'));
      expect(content, contains('String path = \'/users/{id}\';'));
      expect(
        content,
        contains('path = path.replaceAll(\'{id}\', id.toString());'),
      );
    });

    test('should generate client with query parameters', () async {
      final spec = OpenApiSpec(
        openapi: '3.0.0',
        info: Info(title: 'Test API', version: '1.0.0'),
        paths: {
          '/users': PathItem(
            get: Operation(
              operationId: 'searchUsers',
              tags: ['users'],
              parameters: [
                Parameter(
                  name: 'search',
                  parameterIn: 'query',
                  required: false,
                  schema: Schema(type: 'string'),
                ),
                Parameter(
                  name: 'limit',
                  parameterIn: 'query',
                  required: false,
                  schema: Schema(type: 'integer'),
                ),
              ],
              responses: {'200': openapi.Response(description: 'Success')},
            ),
          ),
        },
      );

      final config = GenerationConfig(
        outputPath: tempDir.path,
        clientName: 'TestApiClient',
      );
      await generator.generateClient(spec, config, tempDir.path);

      final usersServiceFile = File(
        path.join(tempDir.path, 'users_service.dart'),
      );
      final content = await usersServiceFile.readAsString();

      // Check query parameter handling
      expect(content, contains('{String? search, int? limit}'));
      expect(content, contains('final queryParameters = <String, dynamic>{};'));
      expect(content, contains('if (search != null)'));
      expect(content, contains('queryParameters[\'search\'] = search;'));
      expect(content, contains('queryParameters: queryParameters'));
    });

    test('should generate client with request body', () async {
      final spec = OpenApiSpec(
        openapi: '3.0.0',
        info: Info(title: 'Test API', version: '1.0.0'),
        paths: {
          '/users': PathItem(
            post: Operation(
              operationId: 'createUser',
              tags: ['users'],
              requestBody: openapi.RequestBody(
                required: true,
                content: {
                  'application/json': openapi.MediaType(
                    schema: Schema(ref: '#/components/schemas/User'),
                  ),
                },
              ),
              responses: {'201': openapi.Response(description: 'Created')},
            ),
          ),
        },
        components: openapi.Components(
          schemas: {'User': Schema(type: 'object')},
        ),
      );

      final config = GenerationConfig(
        outputPath: tempDir.path,
        clientName: 'TestApiClient',
      );
      await generator.generateClient(spec, config, tempDir.path);

      final usersServiceFile = File(
        path.join(tempDir.path, 'users_service.dart'),
      );
      final content = await usersServiceFile.readAsString();

      // Check request body handling (method names are generated in lowercase)
      expect(
        content,
        contains('Future<Response<dynamic>> createuser(User requestData)'),
      );
      expect(content, contains('data: requestData.toJson()'));
    });

    test('should generate client with error handling', () async {
      final spec = OpenApiSpec(
        openapi: '3.0.0',
        info: Info(title: 'Test API', version: '1.0.0'),
        paths: {
          '/users': PathItem(
            get: Operation(
              operationId: 'getUsers',
              tags: ['users'],
              responses: {'200': openapi.Response(description: 'Success')},
            ),
          ),
        },
      );

      final config = GenerationConfig(
        outputPath: tempDir.path,
        clientName: 'TestApiClient',
      );
      await generator.generateClient(spec, config, tempDir.path);

      final usersServiceFile = File(
        path.join(tempDir.path, 'users_service.dart'),
      );
      final content = await usersServiceFile.readAsString();

      // Check error handling
      expect(content, contains('try {'));
      expect(content, contains('} on DioException catch (e) {'));
      expect(
        content,
        contains('throw ApiExceptionHandler.fromDioException(e);'),
      );
      expect(content, contains('} catch (e) {'));
      expect(content, contains('throw NetworkException'));
    });

    test('should generate main API client class', () async {
      final spec = OpenApiSpec(
        openapi: '3.0.0',
        info: Info(title: 'Test API', version: '1.0.0'),
        paths: {
          '/users': PathItem(
            get: Operation(
              operationId: 'getUsers',
              tags: ['users'],
              responses: {'200': openapi.Response(description: 'Success')},
            ),
          ),
          '/posts': PathItem(
            get: Operation(
              operationId: 'getPosts',
              tags: ['posts'],
              responses: {'200': openapi.Response(description: 'Success')},
            ),
          ),
        },
      );

      final config = GenerationConfig(
        outputPath: tempDir.path,
        clientName: 'TestApiClient',
      );
      await generator.generateClient(spec, config, tempDir.path);

      final apiClientFile = File(
        path.join(tempDir.path, 'test_api_client.dart'),
      );
      expect(await apiClientFile.exists(), isTrue);

      final content = await apiClientFile.readAsString();

      // Check main client class
      expect(content, contains('class TestApiClient {'));
      expect(content, contains('final Dio _dio;'));
      expect(content, contains('late final UsersService usersTag;'));
      expect(content, contains('late final PostsService postsTag;'));
      expect(content, contains('usersTag = UsersService(_dio);'));
      expect(content, contains('postsTag = PostsService(_dio);'));
    });

    test('should generate response deserialization', () async {
      final spec = OpenApiSpec(
        openapi: '3.0.0',
        info: Info(title: 'Test API', version: '1.0.0'),
        paths: {
          '/user': PathItem(
            get: Operation(
              operationId: 'getUser',
              tags: ['users'],
              responses: {
                '200': openapi.Response(
                  description: 'Success',
                  content: {
                    'application/json': openapi.MediaType(
                      schema: Schema(ref: '#/components/schemas/User'),
                    ),
                  },
                ),
              },
            ),
          ),
        },
        components: openapi.Components(
          schemas: {'User': Schema(type: 'object')},
        ),
      );

      final config = GenerationConfig(
        outputPath: tempDir.path,
        clientName: 'TestApiClient',
      );
      await generator.generateClient(spec, config, tempDir.path);

      final usersServiceFile = File(
        path.join(tempDir.path, 'users_service.dart'),
      );
      final content = await usersServiceFile.readAsString();

      // Check response deserialization (method names are generated in lowercase)
      expect(content, contains('Future<Response<User>> getuser()'));
      expect(content, contains('final responseData = User.fromJson'));
      expect(content, contains('return Response<User>('));
      expect(content, contains('data: responseData,'));
      expect(content, contains('statusCode: response.statusCode,'));
    });

    test('should handle multiple HTTP methods', () async {
      final spec = OpenApiSpec(
        openapi: '3.0.0',
        info: Info(title: 'Test API', version: '1.0.0'),
        paths: {
          '/users/{id}': PathItem(
            get: Operation(
              operationId: 'getUser',
              tags: ['users'],
              parameters: [
                Parameter(
                  name: 'id',
                  parameterIn: 'path',
                  required: true,
                  schema: Schema(type: 'integer'),
                ),
              ],
              responses: {'200': openapi.Response(description: 'Success')},
            ),
            put: Operation(
              operationId: 'updateUser',
              tags: ['users'],
              parameters: [
                Parameter(
                  name: 'id',
                  parameterIn: 'path',
                  required: true,
                  schema: Schema(type: 'integer'),
                ),
              ],
              requestBody: openapi.RequestBody(
                required: true,
                content: {
                  'application/json': openapi.MediaType(
                    schema: Schema(ref: '#/components/schemas/User'),
                  ),
                },
              ),
              responses: {'200': openapi.Response(description: 'Success')},
            ),
            delete: Operation(
              operationId: 'deleteUser',
              tags: ['users'],
              parameters: [
                Parameter(
                  name: 'id',
                  parameterIn: 'path',
                  required: true,
                  schema: Schema(type: 'integer'),
                ),
              ],
              responses: {'204': openapi.Response(description: 'No Content')},
            ),
          ),
        },
        components: openapi.Components(
          schemas: {'User': Schema(type: 'object')},
        ),
      );

      final config = GenerationConfig(
        outputPath: tempDir.path,
        clientName: 'TestApiClient',
      );
      await generator.generateClient(spec, config, tempDir.path);

      final usersServiceFile = File(
        path.join(tempDir.path, 'users_service.dart'),
      );
      final content = await usersServiceFile.readAsString();

      // Check all HTTP methods are generated (method names are generated in lowercase)
      expect(content, contains('Future<Response<dynamic>> getuser(int id)'));
      expect(
        content,
        contains(
          'Future<Response<dynamic>> updateuser(int id, User requestData)',
        ),
      );
      expect(content, contains('Future<Response<dynamic>> deleteuser(int id)'));
      expect(content, contains('await _dio.get(path)'));
      expect(content, contains('await _dio.put(path'));
      expect(content, contains('await _dio.delete(path)'));
    });
  });
}
