import 'dart:io';
import 'package:openapi_dart_gen/openapi_dart_gen.dart';
import 'package:test/test.dart';
import 'package:path/path.dart' as path;
import 'package:openapi_dart_gen/src/models/openapi_models.dart';

void main() {
  group('ModelGenerator Tests', () {
    late ModelGenerator generator;
    late Directory tempDir;

    setUp(() async {
      generator = ModelGenerator();
      tempDir = await Directory.systemTemp.createTemp('model_generator_test');
    });

    tearDown(() async {
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }
    });

    test('should generate basic model class', () async {
      final schemas = {
        'User': Schema(
          type: 'object',
          properties: {
            'id': Schema(type: 'integer'),
            'name': Schema(type: 'string'),
            'email': Schema(type: 'string'),
          },
          required: ['id', 'name'],
        ),
      };

      await generator.generateModels(schemas, tempDir.path);

      final userFile = File(path.join(tempDir.path, 'user.dart'));
      expect(await userFile.exists(), isTrue);

      final content = await userFile.readAsString();

      // Check class declaration
      expect(content, contains('class User {'));

      // Check properties
      expect(content, contains('final int id;'));
      expect(content, contains('final String name;'));
      expect(content, contains('final String? email;'));

      // Check constructor
      expect(content, contains('const User({'));
      expect(content, contains('required this.id,'));
      expect(content, contains('required this.name,'));
      expect(content, contains('this.email,'));

      // Check fromJson factory
      expect(
        content,
        contains('factory User.fromJson(Map<String, dynamic> json)'),
      );

      // Check toJson method
      expect(content, contains('Map<String, dynamic> toJson()'));

      // Check toString method
      expect(content, contains('@override'));
      expect(content, contains('String toString()'));
    });

    test('should generate model with schema references', () async {
      final schemas = {
        'User': Schema(
          type: 'object',
          properties: {
            'id': Schema(type: 'integer'),
            'profile': Schema(ref: '#/components/schemas/Profile'),
          },
          required: ['id'],
        ),
        'Profile': Schema(
          type: 'object',
          properties: {
            'bio': Schema(type: 'string'),
            'avatar': Schema(type: 'string'),
          },
        ),
      };

      await generator.generateModels(schemas, tempDir.path);

      final userFile = File(path.join(tempDir.path, 'user.dart'));
      final content = await userFile.readAsString();

      // Check that it imports models.dart for references
      expect(content, contains("import 'models.dart';"));

      // Check property type is correct
      expect(content, contains('final Profile? profile;'));

      // Check fromJson handles reference
      expect(content, contains('Profile.fromJson'));

      // Check toJson handles reference
      expect(content, contains('profile?.toJson()'));
    });

    test('should generate model with array properties', () async {
      final schemas = {
        'UserList': Schema(
          type: 'object',
          properties: {
            'users': Schema(
              type: 'array',
              items: Schema(ref: '#/components/schemas/User'),
            ),
            'tags': Schema(
              type: 'array',
              items: Schema(type: 'string'),
            ),
          },
          required: ['users'],
        ),
        'User': Schema(
          type: 'object',
          properties: {'name': Schema(type: 'string')},
        ),
      };

      await generator.generateModels(schemas, tempDir.path);

      final userListFile = File(path.join(tempDir.path, 'user_list.dart'));
      final content = await userListFile.readAsString();

      // Check array property types
      expect(content, contains('final List<User> users;'));
      expect(content, contains('final List<String>? tags;'));

      // Check fromJson handles arrays
      expect(content, contains('.map((item) => User.fromJson'));
      expect(content, contains('.toList()'));
    });

    test(
      'should generate model with nullable and composition schemas',
      () async {
        final schemas = {
          'FlexibleType': Schema(
            anyOf: [
              Schema(type: 'string'),
              Schema(type: 'null'),
            ],
          ),
          'Container': Schema(
            type: 'object',
            properties: {
              'value': Schema(
                oneOf: [
                  Schema(type: 'string'),
                  Schema(type: 'integer'),
                ],
              ),
              'flexible': Schema(ref: '#/components/schemas/FlexibleType'),
            },
          ),
        };

        await generator.generateModels(schemas, tempDir.path);

        final containerFile = File(path.join(tempDir.path, 'container.dart'));
        final content = await containerFile.readAsString();

        // Check that composition schemas are handled
        expect(content, contains('final String? value;'));
        expect(content, contains('final FlexibleType? flexible;'));
      },
    );

    test('should generate models barrel file', () async {
      final schemas = {
        'User': Schema(type: 'object'),
        'Profile': Schema(type: 'object'),
        'Settings': Schema(type: 'object'),
      };

      await generator.generateModels(schemas, tempDir.path);

      final modelsFile = File(path.join(tempDir.path, 'models.dart'));
      expect(await modelsFile.exists(), isTrue);

      final content = await modelsFile.readAsString();

      // Check exports
      expect(content, contains("export 'user.dart';"));
      expect(content, contains("export 'profile.dart';"));
      expect(content, contains("export 'settings.dart';"));
    });

    test('should handle complex nested schemas', () async {
      final schemas = {
        'NestedObject': Schema(
          type: 'object',
          properties: {
            'metadata': Schema(
              type: 'object',
              properties: {
                'created': Schema(type: 'string', format: 'date-time'),
                'tags': Schema(
                  type: 'array',
                  items: Schema(type: 'string'),
                ),
              },
            ),
            'data': Schema(
              type: 'array',
              items: Schema(
                type: 'object',
                properties: {
                  'key': Schema(type: 'string'),
                  'value': Schema(type: 'string'),
                },
              ),
            ),
          },
        ),
      };

      await generator.generateModels(schemas, tempDir.path);

      final nestedFile = File(path.join(tempDir.path, 'nested_object.dart'));
      final content = await nestedFile.readAsString();

      // Check nested object handling
      expect(content, contains('final Map<String, dynamic>? metadata;'));
      expect(content, contains('final List<Map<String, dynamic>>? data;'));
    });

    test('should generate proper documentation comments', () async {
      final schemas = {
        'DocumentedModel': Schema(
          type: 'object',
          description: 'A well-documented model class',
          properties: {
            'id': Schema(type: 'integer', description: 'The unique identifier'),
            'name': Schema(type: 'string', description: 'The display name'),
          },
        ),
      };

      await generator.generateModels(schemas, tempDir.path);

      final modelFile = File(path.join(tempDir.path, 'documented_model.dart'));
      final content = await modelFile.readAsString();

      // Check class documentation
      expect(content, contains('/// A well-documented model class'));
      expect(content, contains('class DocumentedModel'));
    });

    test('should handle enum-like schemas', () async {
      final schemas = {
        'Status': Schema(
          type: 'string',
          enumValues: ['active', 'inactive', 'pending'],
        ),
        'Item': Schema(
          type: 'object',
          properties: {'status': Schema(ref: '#/components/schemas/Status')},
        ),
      };

      await generator.generateModels(schemas, tempDir.path);

      final statusFile = File(path.join(tempDir.path, 'status.dart'));
      final itemFile = File(path.join(tempDir.path, 'item.dart'));

      expect(await statusFile.exists(), isTrue);
      expect(await itemFile.exists(), isTrue);

      final itemContent = await itemFile.readAsString();
      expect(itemContent, contains('final Status? status;'));
    });
  });
}
