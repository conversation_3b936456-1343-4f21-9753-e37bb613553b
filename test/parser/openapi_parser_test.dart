import 'dart:convert';
import 'dart:io';
import 'package:openapi_dart_gen/openapi_dart_gen.dart';
import 'package:test/test.dart';

void main() {
  group('OpenApiParser Tests', () {
    late OpenApiParser parser;

    setUp(() {
      parser = OpenApiParser();
    });

    test('should parse basic OpenAPI specification', () async {
      final basicSpec = {
        'openapi': '3.0.0',
        'info': {
          'title': 'Test API',
          'version': '1.0.0',
          'description': 'A test API specification',
        },
        'paths': <String, dynamic>{},
        'components': {'schemas': <String, dynamic>{}},
      };

      final spec = parser.parseFrom<PERSON>son(jsonEncode(basicSpec));

      expect(spec.openapi, equals('3.0.0'));
      expect(spec.info.title, equals('Test API'));
      expect(spec.info.version, equals('1.0.0'));
      expect(spec.info.description, equals('A test API specification'));
      expect(spec.paths, isEmpty);
      expect(spec.components?.schemas, isEmpty);
    });

    test('should parse OpenAPI specification with schemas', () async {
      final specWithSchemas = {
        'openapi': '3.0.0',
        'info': {'title': 'Test API', 'version': '1.0.0'},
        'paths': <String, dynamic>{},
        'components': {
          'schemas': {
            'User': {
              'type': 'object',
              'required': ['id', 'name'],
              'properties': {
                'id': {'type': 'integer', 'format': 'int64'},
                'name': {'type': 'string'},
                'email': {'type': 'string', 'format': 'email'},
              },
            },
          },
        },
      };

      final spec = parser.parseFromJson(jsonEncode(specWithSchemas));

      expect(spec.components?.schemas, hasLength(1));
      expect(spec.components?.schemas?.containsKey('User'), isTrue);

      final userSchema = spec.components!.schemas!['User']!;
      expect(userSchema.type, equals('object'));
      expect(userSchema.required, containsAll(['id', 'name']));
      expect(userSchema.properties, hasLength(3));
      expect(userSchema.properties?['id']?.type, equals('integer'));
      expect(userSchema.properties?['name']?.type, equals('string'));
      expect(userSchema.properties?['email']?.type, equals('string'));
    });

    test(
      'should parse OpenAPI specification with paths and operations',
      () async {
        final specWithPaths = {
          'openapi': '3.0.0',
          'info': {'title': 'Test API', 'version': '1.0.0'},
          'paths': {
            '/users': {
              'get': {
                'operationId': 'getUsers',
                'summary': 'Get all users',
                'tags': ['users'],
                'responses': {
                  '200': {
                    'description': 'Successful response',
                    'content': {
                      'application/json': {
                        'schema': {
                          'type': 'array',
                          'items': {'\$ref': '#/components/schemas/User'},
                        },
                      },
                    },
                  },
                },
              },
              'post': {
                'operationId': 'createUser',
                'summary': 'Create a new user',
                'tags': ['users'],
                'requestBody': {
                  'required': true,
                  'content': {
                    'application/json': {
                      'schema': {'\$ref': '#/components/schemas/User'},
                    },
                  },
                },
                'responses': {
                  '201': {'description': 'User created successfully'},
                },
              },
            },
          },
          'components': {
            'schemas': {
              'User': {
                'type': 'object',
                'properties': {
                  'id': {'type': 'integer'},
                  'name': {'type': 'string'},
                },
              },
            },
          },
        };

        final spec = parser.parseFromJson(jsonEncode(specWithPaths));

        expect(spec.paths, hasLength(1));
        expect(spec.paths.containsKey('/users'), isTrue);

        final usersPath = spec.paths['/users']!;
        expect(usersPath.get, isNotNull);
        expect(usersPath.post, isNotNull);

        final getOperation = usersPath.get!;
        expect(getOperation.operationId, equals('getUsers'));
        expect(getOperation.summary, equals('Get all users'));
        expect(getOperation.tags, contains('users'));
        expect(getOperation.responses, hasLength(1));

        final postOperation = usersPath.post!;
        expect(postOperation.operationId, equals('createUser'));
        expect(postOperation.requestBody, isNotNull);
        expect(postOperation.requestBody!.required, isTrue);
      },
    );

    test('should handle schema references correctly', () async {
      final specWithRefs = {
        'openapi': '3.0.0',
        'info': {'title': 'Test API', 'version': '1.0.0'},
        'paths': <String, dynamic>{},
        'components': {
          'schemas': {
            'User': {
              'type': 'object',
              'properties': {
                'profile': {'\$ref': '#/components/schemas/Profile'},
              },
            },
            'Profile': {
              'type': 'object',
              'properties': {
                'bio': {'type': 'string'},
                'avatar': {'type': 'string'},
              },
            },
          },
        },
      };

      final spec = parser.parseFromJson(jsonEncode(specWithRefs));

      final userSchema = spec.components!.schemas!['User']!;
      final profileProperty = userSchema.properties!['profile']!;
      expect(profileProperty.ref, equals('#/components/schemas/Profile'));

      final profileSchema = spec.components!.schemas!['Profile']!;
      expect(profileSchema.type, equals('object'));
      expect(profileSchema.properties, hasLength(2));
    });

    test('should parse from JSON file', () async {
      // Create a temporary test file
      final tempFile = File('test_spec.json');
      final testSpec = {
        'openapi': '3.0.0',
        'info': {'title': 'File Test API', 'version': '1.0.0'},
        'paths': <String, dynamic>{},
        'components': {'schemas': <String, dynamic>{}},
      };

      await tempFile.writeAsString(jsonEncode(testSpec));

      try {
        final spec = await parser.parseFromFile('test_spec.json');
        expect(spec.info.title, equals('File Test API'));
      } finally {
        // Clean up
        if (await tempFile.exists()) {
          await tempFile.delete();
        }
      }
    });

    test('should handle parsing errors gracefully', () async {
      final invalidSpec = {
        'openapi': '3.0.0',
        // Missing required 'info' field
        'paths': <String, dynamic>{},
      };

      expect(
        () => parser.parseFromJson(jsonEncode(invalidSpec)),
        throwsA(isA<Exception>()),
      );
    });

    test('should handle complex schema types', () async {
      final complexSpec = {
        'openapi': '3.0.0',
        'info': {'title': 'Complex API', 'version': '1.0.0'},
        'paths': <String, dynamic>{},
        'components': {
          'schemas': {
            'ComplexType': {
              'anyOf': [
                {'type': 'string'},
                {'type': 'integer'},
                {'\$ref': '#/components/schemas/User'},
              ],
            },
            'User': {
              'type': 'object',
              'properties': {
                'name': {'type': 'string'},
              },
            },
          },
        },
      };

      final spec = parser.parseFromJson(jsonEncode(complexSpec));
      final complexSchema = spec.components!.schemas!['ComplexType']!;

      expect(complexSchema.anyOf, hasLength(3));
      expect(complexSchema.anyOf![0].type, equals('string'));
      expect(complexSchema.anyOf![1].type, equals('integer'));
      expect(complexSchema.anyOf![2].ref, equals('#/components/schemas/User'));
    });
  });
}
