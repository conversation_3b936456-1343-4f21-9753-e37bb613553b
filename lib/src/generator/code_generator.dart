import 'dart:io';
import 'package:path/path.dart' as path;
import '../models/openapi_models.dart';
import '../cli/cli_runner.dart';
import 'model_generator.dart';
import 'client_generator.dart';

/// Main code generator that orchestrates the generation process
class CodeGenerator {
  final ModelGenerator _modelGenerator = ModelGenerator();
  final ClientGenerator _clientGenerator = ClientGenerator();

  /// Generate all Dart code from OpenAPI specification
  Future<void> generate(OpenApiSpec spec, GenerationConfig config) async {
    // Create output directories
    await _createDirectories(config.outputPath);

    // Generate models from schemas
    if (spec.components?.schemas != null) {
      await _modelGenerator.generateModels(
        spec.components!.schemas!,
        path.join(config.outputPath, 'models'),
      );
    }

    // Generate API client
    await _clientGenerator.generateClient(
      spec,
      config,
      path.join(config.outputPath, 'client'),
    );

    // Generate barrel file for easy imports
    await _generateBarrelFile(config.outputPath);
  }

  /// Create necessary output directories
  Future<void> _createDirectories(String outputPath) async {
    final directories = [
      outputPath,
      path.join(outputPath, 'models'),
      path.join(outputPath, 'client'),
    ];

    for (final dir in directories) {
      final directory = Directory(dir);
      if (!directory.existsSync()) {
        await directory.create(recursive: true);
      }
    }
  }

  /// Generate a barrel file that exports all generated code
  Future<void> _generateBarrelFile(String outputPath) async {
    final barrelFile = File(path.join(outputPath, 'api.dart'));
    final buffer = StringBuffer();

    buffer.writeln('// Generated barrel file for OpenAPI Dart Generator');
    buffer.writeln(
      '// This file exports all generated models and client classes',
    );
    buffer.writeln();

    // Export models
    final modelsDir = Directory(path.join(outputPath, 'models'));
    if (modelsDir.existsSync()) {
      buffer.writeln('// Models');
      await for (final entity in modelsDir.list()) {
        if (entity is File && entity.path.endsWith('.dart')) {
          final fileName = path.basename(entity.path);
          buffer.writeln("export 'models/$fileName';");
        }
      }
      buffer.writeln();
    }

    // Export client
    final clientDir = Directory(path.join(outputPath, 'client'));
    if (clientDir.existsSync()) {
      buffer.writeln('// API Client');
      await for (final entity in clientDir.list()) {
        if (entity is File && entity.path.endsWith('.dart')) {
          final fileName = path.basename(entity.path);
          buffer.writeln("export 'client/$fileName';");
        }
      }
    }

    await barrelFile.writeAsString(buffer.toString());
  }
}

/// Utility class for code generation helpers
class CodeGenUtils {
  /// Convert OpenAPI path parameter to Dart parameter name
  static String pathParamToDartParam(String param) {
    // Remove curly braces and convert to camelCase
    final cleaned = param.replaceAll(RegExp(r'[{}]'), '');
    return toCamelCase(cleaned);
  }

  /// Convert string to camelCase
  static String toCamelCase(String input) {
    if (input.isEmpty) return input;

    final words = input.split(RegExp(r'[_\-\s]+'));
    if (words.isEmpty) return input;

    final first = words.first.toLowerCase();
    final rest = words
        .skip(1)
        .map(
          (word) => word.isEmpty
              ? ''
              : word[0].toUpperCase() + word.substring(1).toLowerCase(),
        );

    return first + rest.join('');
  }

  /// Convert string to PascalCase
  static String toPascalCase(String input) {
    if (input.isEmpty) return input;

    // If the input is already in PascalCase or camelCase, preserve it
    if (RegExp(r'^[A-Z][a-zA-Z0-9]*$').hasMatch(input)) {
      return input; // Already PascalCase
    }

    if (RegExp(r'^[a-z][a-zA-Z0-9]*$').hasMatch(input)) {
      return input[0].toUpperCase() +
          input.substring(1); // Convert camelCase to PascalCase
    }

    // Handle snake_case, kebab-case, and space-separated words
    final words = input.split(RegExp(r'[_\-\s]+'));
    return words
        .map(
          (word) => word.isEmpty
              ? ''
              : word[0].toUpperCase() + word.substring(1).toLowerCase(),
        )
        .join('');
  }

  /// Convert string to snake_case
  static String toSnakeCase(String input) {
    return input
        .replaceAllMapped(
          RegExp(r'[A-Z]'),
          (match) => '_${match.group(0)!.toLowerCase()}',
        )
        .replaceAll(RegExp(r'^_'), '')
        .replaceAll(RegExp(r'[_\-\s]+'), '_')
        .toLowerCase();
  }

  /// Sanitize Dart identifier
  static String sanitizeDartIdentifier(String input) {
    // Replace invalid characters with underscore
    String sanitized = input.replaceAll(RegExp(r'[^a-zA-Z0-9_]'), '_');

    // Ensure it doesn't start with a number
    if (sanitized.isNotEmpty && RegExp(r'^[0-9]').hasMatch(sanitized)) {
      sanitized = '_$sanitized';
    }

    // Handle Dart reserved keywords
    const reservedKeywords = {
      'abstract',
      'as',
      'assert',
      'async',
      'await',
      'break',
      'case',
      'catch',
      'class',
      'const',
      'continue',
      'default',
      'deferred',
      'do',
      'dynamic',
      'else',
      'enum',
      'export',
      'extends',
      'external',
      'factory',
      'false',
      'final',
      'finally',
      'for',
      'function',
      'get',
      'hide',
      'if',
      'implements',
      'import',
      'in',
      'interface',
      'is',
      'library',
      'mixin',
      'new',
      'null',
      'on',
      'operator',
      'part',
      'rethrow',
      'return',
      'set',
      'show',
      'static',
      'super',
      'switch',
      'sync',
      'this',
      'throw',
      'true',
      'try',
      'typedef',
      'var',
      'void',
      'while',
      'with',
      'yield',
    };

    if (reservedKeywords.contains(sanitized.toLowerCase())) {
      sanitized = '${sanitized}_';
    }

    return sanitized;
  }

  /// Generate Dart documentation comment
  static String generateDocComment(String? description, {int indent = 0}) {
    if (description == null || description.trim().isEmpty) {
      return '';
    }

    final indentStr = ' ' * indent;
    final lines = description.trim().split('\n');
    final buffer = StringBuffer();

    if (lines.length == 1) {
      buffer.writeln('$indentStr/// ${lines.first}');
    } else {
      buffer.writeln('$indentStr///');
      for (final line in lines) {
        buffer.writeln('$indentStr/// ${line.trim()}');
      }
      buffer.writeln('$indentStr///');
    }

    return buffer.toString();
  }

  /// Check if a string is a valid Dart type
  static bool isValidDartType(String type) {
    const validTypes = {
      'String',
      'int',
      'double',
      'bool',
      'dynamic',
      'Object',
      'List',
      'Map',
      'Set',
      'Iterable',
      'Future',
      'Stream',
    };

    // Check basic types
    if (validTypes.contains(type)) return true;

    // Check generic types (simplified check)
    if (type.contains('<') && type.contains('>')) {
      final baseType = type.substring(0, type.indexOf('<'));
      return validTypes.contains(baseType);
    }

    // Check if it looks like a valid class name (PascalCase)
    return RegExp(r'^[A-Z][a-zA-Z0-9]*$').hasMatch(type);
  }

  /// Escape string for Dart string literal
  static String escapeString(String input) {
    return input
        .replaceAll('\\', '\\\\')
        .replaceAll("'", "\\'")
        .replaceAll('"', '\\"')
        .replaceAll('\n', '\\n')
        .replaceAll('\r', '\\r')
        .replaceAll('\t', '\\t');
  }
}
