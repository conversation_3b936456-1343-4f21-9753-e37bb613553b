import 'dart:io';
import 'package:path/path.dart' as path;
import '../models/openapi_models.dart';
import '../models/openapi_models_extended.dart' as openapi;
import '../cli/cli_runner.dart';
import 'code_generator.dart';

/// Generator for Dio-based API client from OpenAPI specification
class ClientGenerator {
  /// Generate API client
  Future<void> generateClient(
    OpenApiSpec spec,
    GenerationConfig config,
    String outputPath,
  ) async {
    final outputDir = Directory(outputPath);
    if (!outputDir.existsSync()) {
      await outputDir.create(recursive: true);
    }

    // Generate main API client class
    await _generateMainClient(spec, config, outputPath);

    // Generate tag-based service classes
    await _generateTagServices(spec, outputPath);
  }

  /// Generate main API client class
  Future<void> _generateMainClient(
    OpenApiSpec spec,
    GenerationConfig config,
    String outputPath,
  ) async {
    final clientName = config.clientName;
    final fileName = CodeGenUtils.toSnakeCase(clientName);
    final filePath = path.join(outputPath, '$fileName.dart');

    final buffer = StringBuffer();

    // File header and imports
    buffer.writeln('// Generated API client for ${spec.info.title}');
    buffer.writeln('// This file is auto-generated. Do not edit manually.');
    buffer.writeln();
    buffer.writeln("import 'package:dio/dio.dart';");
    buffer.writeln();

    // Import tag services
    final tags = _extractTags(spec);
    for (final tag in tags) {
      final serviceFileName = CodeGenUtils.toSnakeCase('${tag}_service');
      buffer.writeln("import '$serviceFileName.dart';");
    }
    buffer.writeln();

    // Generate client class documentation
    buffer.write(
      CodeGenUtils.generateDocComment('API client for ${spec.info.title}'),
    );
    if (spec.info.description != null) {
      buffer.write(CodeGenUtils.generateDocComment(spec.info.description));
    }

    // Generate client class
    buffer.writeln('class $clientName {');
    buffer.writeln('  final Dio _dio;');
    buffer.writeln();

    // Generate tag service properties
    for (final tag in tags) {
      final serviceName = CodeGenUtils.toPascalCase('${tag}_service');
      final propertyName = CodeGenUtils.toCamelCase('${tag}_tag');
      buffer.writeln('  late final $serviceName $propertyName;');
    }
    buffer.writeln();

    // Generate constructor
    buffer.writeln('  $clientName({');
    buffer.writeln('    String? baseUrl,');
    buffer.writeln('    Dio? dio,');
    buffer.writeln('    Map<String, String>? headers,');
    buffer.writeln('    Duration? connectTimeout,');
    buffer.writeln('    Duration? receiveTimeout,');
    buffer.writeln('  }) : _dio = dio ?? Dio() {');

    // Configure Dio
    buffer.writeln('    // Configure base options');
    if (config.baseUrl != null) {
      buffer.writeln(
        "    _dio.options.baseUrl = baseUrl ?? '${config.baseUrl}';",
      );
    } else if (spec.servers?.isNotEmpty == true) {
      buffer.writeln(
        "    _dio.options.baseUrl = baseUrl ?? '${spec.servers!.first.url}';",
      );
    } else {
      buffer.writeln("    _dio.options.baseUrl = baseUrl ?? '';");
    }

    buffer.writeln(
      '    _dio.options.connectTimeout = connectTimeout ?? const Duration(seconds: 30);',
    );
    buffer.writeln(
      '    _dio.options.receiveTimeout = receiveTimeout ?? const Duration(seconds: 30);',
    );
    buffer.writeln();
    buffer.writeln('    // Set default headers');
    buffer.writeln(
      "    _dio.options.headers['Content-Type'] = 'application/json';",
    );
    buffer.writeln("    _dio.options.headers['Accept'] = 'application/json';");
    buffer.writeln();
    buffer.writeln('    // Add custom headers');
    buffer.writeln('    if (headers != null) {');
    buffer.writeln('      _dio.options.headers.addAll(headers);');
    buffer.writeln('    }');
    buffer.writeln();

    // Initialize tag services
    for (final tag in tags) {
      final serviceName = CodeGenUtils.toPascalCase('${tag}_service');
      final propertyName = CodeGenUtils.toCamelCase('${tag}_tag');
      buffer.writeln('    $propertyName = $serviceName(_dio);');
    }

    buffer.writeln('  }');
    buffer.writeln();

    // Generate getter for Dio instance
    buffer.writeln('  /// Access to the underlying Dio instance');
    buffer.writeln('  Dio get dio => _dio;');
    buffer.writeln();

    // Generate method to add interceptors
    buffer.writeln('  /// Add an interceptor to the Dio instance');
    buffer.writeln('  void addInterceptor(Interceptor interceptor) {');
    buffer.writeln('    _dio.interceptors.add(interceptor);');
    buffer.writeln('  }');
    buffer.writeln();

    // Generate method to set authorization header
    buffer.writeln('  /// Set authorization header');
    buffer.writeln(
      '  void setAuthorizationHeader(String token, {String scheme = "Bearer"}) {',
    );
    buffer.writeln(
      '    _dio.options.headers["Authorization"] = "\$scheme \$token";',
    );
    buffer.writeln('  }');
    buffer.writeln();

    // Generate method to remove authorization header
    buffer.writeln('  /// Remove authorization header');
    buffer.writeln('  void removeAuthorizationHeader() {');
    buffer.writeln('    _dio.options.headers.remove("Authorization");');
    buffer.writeln('  }');

    buffer.writeln('}');

    // Write to file
    final file = File(filePath);
    await file.writeAsString(buffer.toString());
  }

  /// Generate tag-based service classes
  Future<void> _generateTagServices(OpenApiSpec spec, String outputPath) async {
    final tags = _extractTags(spec);

    for (final tag in tags) {
      await _generateTagService(spec, tag, outputPath);
    }
  }

  /// Generate a single tag service class
  Future<void> _generateTagService(
    OpenApiSpec spec,
    String tag,
    String outputPath,
  ) async {
    final serviceName = CodeGenUtils.toPascalCase('${tag}_service');
    final fileName = CodeGenUtils.toSnakeCase('${tag}_service');
    final filePath = path.join(outputPath, '$fileName.dart');

    final buffer = StringBuffer();

    // File header and imports
    buffer.writeln('// Generated service class for $tag operations');
    buffer.writeln('// This file is auto-generated. Do not edit manually.');
    buffer.writeln();
    buffer.writeln("import 'package:dio/dio.dart';");

    // Check if we need to import models
    final operations = _getOperationsForTag(spec, tag);
    final needsModels = operations.any(
      (op) =>
          op.operation.requestBody != null || _hasTypedResponse(op.operation),
    );

    if (needsModels) {
      buffer.writeln("import '../models/models.dart';");
    }

    // Always import exceptions for error handling
    buffer.writeln("import 'package:openapi_dart_gen/openapi_dart_gen.dart';");

    buffer.writeln();

    // Generate service class
    buffer.write(
      CodeGenUtils.generateDocComment('Service class for $tag operations'),
    );
    buffer.writeln('class $serviceName {');
    buffer.writeln('  final Dio _dio;');
    buffer.writeln();
    buffer.writeln('  const $serviceName(this._dio);');
    buffer.writeln();

    // Generate methods for each operation in this tag
    for (final operation in operations) {
      _generateOperationMethod(buffer, operation);
    }

    buffer.writeln('}');

    // Write to file
    final file = File(filePath);
    await file.writeAsString(buffer.toString());
  }

  /// Generate method for a single operation
  void _generateOperationMethod(StringBuffer buffer, OperationInfo operation) {
    final methodName = _generateMethodName(operation);
    final returnType = _generateReturnType(operation);

    // Method documentation
    if (operation.operation.summary != null) {
      buffer.write(
        CodeGenUtils.generateDocComment(operation.operation.summary, indent: 2),
      );
    }
    if (operation.operation.description != null) {
      buffer.write(
        CodeGenUtils.generateDocComment(
          operation.operation.description,
          indent: 2,
        ),
      );
    }

    // Method signature
    buffer.write('  Future<$returnType> $methodName(');

    // Generate parameters
    final parameters = _generateMethodParameters(operation);
    buffer.write(parameters);
    buffer.writeln(') async {');

    // Method body
    _generateMethodBody(buffer, operation);

    buffer.writeln('  }');
    buffer.writeln();
  }

  /// Generate method name from operation
  String _generateMethodName(OperationInfo operation) {
    if (operation.operation.operationId != null) {
      return CodeGenUtils.toCamelCase(operation.operation.operationId!);
    }

    // Generate name from HTTP method and path
    final method = operation.httpMethod.toLowerCase();
    final pathParts = operation.path
        .split('/')
        .where((part) => part.isNotEmpty);
    final pathName = pathParts
        .map(
          (part) => part.startsWith('{')
              ? 'by${CodeGenUtils.toPascalCase(part.replaceAll(RegExp(r'[{}]'), ''))}'
              : CodeGenUtils.toPascalCase(part),
        )
        .join('');

    return CodeGenUtils.toCamelCase('${method}_$pathName');
  }

  /// Generate return type for operation
  String _generateReturnType(OperationInfo operation) {
    // Look for successful response (200, 201, etc.)
    final successResponse = _getSuccessResponse(operation.operation);
    if (successResponse != null) {
      final jsonContent = successResponse.content?['application/json'];
      if (jsonContent?.schema != null) {
        final schema = jsonContent!.schema!;
        final dartType = schema.dartType;
        return 'Response<$dartType>';
      }
    }

    // Default to Response<dynamic> if no typed response found
    return 'Response<dynamic>';
  }

  /// Get the success response from operation responses
  openapi.Response? _getSuccessResponse(Operation operation) {
    // Look for 2xx responses in order of preference
    const successCodes = ['200', '201', '202', '204'];

    for (final code in successCodes) {
      if (operation.responses.containsKey(code)) {
        return operation.responses[code];
      }
    }

    // If no specific success code found, look for any 2xx response
    for (final entry in operation.responses.entries) {
      final code = entry.key;
      if (code.startsWith('2')) {
        return entry.value;
      }
    }

    return null;
  }

  /// Generate response deserialization code
  void _generateResponseDeserialization(
    StringBuffer buffer,
    Schema schema, [
    String indent = '    ',
  ]) {
    if (schema.ref != null) {
      // Handle schema reference - deserialize to model class
      final refParts = schema.ref!.split('/');
      if (refParts.length >= 3 && refParts[refParts.length - 2] == 'schemas') {
        final schemaName = refParts.last;
        final className = CodeGenUtils.toPascalCase(schemaName);
        buffer.writeln(
          '${indent}final responseData = $className.fromJson(response.data as Map<String, dynamic>);',
        );
        buffer.writeln('${indent}return Response<$className>(');
        buffer.writeln('$indent  data: responseData,');
        buffer.writeln('$indent  statusCode: response.statusCode,');
        buffer.writeln('$indent  statusMessage: response.statusMessage,');
        buffer.writeln('$indent  headers: response.headers,');
        buffer.writeln('$indent  requestOptions: response.requestOptions,');
        buffer.writeln('$indent);');
        return;
      }
    }

    if (schema.type == 'array' && schema.items != null) {
      // Handle array responses
      final itemSchema = schema.items!;
      if (itemSchema.ref != null) {
        final refParts = itemSchema.ref!.split('/');
        if (refParts.length >= 3 &&
            refParts[refParts.length - 2] == 'schemas') {
          final schemaName = refParts.last;
          final className = CodeGenUtils.toPascalCase(schemaName);
          buffer.writeln(
            '${indent}final responseDataList = (response.data as List<dynamic>)',
          );
          buffer.writeln(
            '$indent    .map((item) => $className.fromJson(item as Map<String, dynamic>))',
          );
          buffer.writeln('$indent    .toList();');
          buffer.writeln('${indent}return Response<List<$className>>(');
          buffer.writeln('$indent  data: responseDataList,');
          buffer.writeln('$indent  statusCode: response.statusCode,');
          buffer.writeln('$indent  statusMessage: response.statusMessage,');
          buffer.writeln('$indent  headers: response.headers,');
          buffer.writeln('$indent  requestOptions: response.requestOptions,');
          buffer.writeln('$indent);');
          return;
        }
      }
    }

    // For primitive types or unhandled cases, return the response as-is
    buffer.writeln('${indent}return response;');
  }

  /// Generate method parameters
  String _generateMethodParameters(OperationInfo operation) {
    final positionalParams = <String>[];
    final namedParams = <String>[];

    // Add path parameters as required positional parameters
    final pathParams =
        operation.operation.parameters
            ?.where((p) => p.parameterIn == 'path')
            .toList() ??
        [];

    for (final param in pathParams) {
      final paramName = CodeGenUtils.toCamelCase(param.name);
      final paramType = param.schema?.dartType ?? 'String';

      if (param.required == true) {
        positionalParams.add('$paramType $paramName');
      } else {
        positionalParams.add('$paramType? $paramName');
      }
    }

    // Add request body parameter if present
    final requestBody = operation.operation.requestBody;
    if (requestBody != null) {
      final jsonContent = requestBody.content['application/json'];
      if (jsonContent?.schema != null) {
        final schema = jsonContent!.schema!;
        final bodyType = schema.dartType;
        final isRequired = requestBody.required == true;

        if (isRequired) {
          positionalParams.add('$bodyType requestData');
        } else {
          namedParams.add('$bodyType? requestData');
        }
      }
    }

    // Add query parameters as optional named parameters
    final queryParams =
        operation.operation.parameters
            ?.where((p) => p.parameterIn == 'query')
            .toList() ??
        [];

    for (final param in queryParams) {
      final paramName = CodeGenUtils.toCamelCase(param.name);
      final paramType = param.schema?.dartType ?? 'String';
      namedParams.add('$paramType? $paramName');
    }

    // Add header parameters as optional named parameters
    final headerParams =
        operation.operation.parameters
            ?.where((p) => p.parameterIn == 'header')
            .toList() ??
        [];

    for (final param in headerParams) {
      final paramName = CodeGenUtils.toCamelCase(param.name);
      final paramType = param.schema?.dartType ?? 'String';
      namedParams.add('$paramType? $paramName');
    }

    // Build final parameter string
    final allParams = <String>[];

    // Add positional parameters
    allParams.addAll(positionalParams);

    // Add named parameters if any
    if (namedParams.isNotEmpty) {
      allParams.add('{${namedParams.join(', ')}}');
    }

    return allParams.join(', ');
  }

  /// Generate method body
  void _generateMethodBody(StringBuffer buffer, OperationInfo operation) {
    // Build path with parameters
    String pathTemplate = operation.path;
    final pathParams =
        operation.operation.parameters
            ?.where((p) => p.parameterIn == 'path')
            .toList() ??
        [];

    if (pathParams.isNotEmpty) {
      buffer.writeln('    String path = \'$pathTemplate\';');

      // Replace each path parameter
      for (final param in pathParams) {
        final paramName = CodeGenUtils.toCamelCase(param.name);
        buffer.writeln(
          '    path = path.replaceAll(\'{${param.name}}\', $paramName.toString());',
        );
      }
    } else {
      buffer.writeln('    const path = \'$pathTemplate\';');
    }

    // Build query parameters
    final queryParams =
        operation.operation.parameters
            ?.where((p) => p.parameterIn == 'query')
            .toList() ??
        [];

    if (queryParams.isNotEmpty) {
      buffer.writeln('    final queryParameters = <String, dynamic>{};');
      for (final param in queryParams) {
        final paramName = CodeGenUtils.toCamelCase(param.name);
        buffer.writeln('    if ($paramName != null) {');
        buffer.writeln(
          '      queryParameters[\'${param.name}\'] = $paramName;',
        );
        buffer.writeln('    }');
      }
    }

    // Handle request body
    final requestBody = operation.operation.requestBody;
    String? requestBodyParam;
    if (requestBody != null) {
      // Find JSON content type
      final jsonContent = requestBody.content['application/json'];
      if (jsonContent?.schema != null) {
        requestBodyParam = 'requestData';
      }
    }

    // Handle header parameters
    final headerParams =
        operation.operation.parameters
            ?.where((p) => p.parameterIn == 'header')
            .toList() ??
        [];

    if (headerParams.isNotEmpty) {
      buffer.writeln('    final headers = <String, String>{};');
      for (final param in headerParams) {
        final paramName = CodeGenUtils.toCamelCase(param.name);
        buffer.writeln('    if ($paramName != null) {');
        buffer.writeln(
          '      headers[\'${param.name}\'] = $paramName.toString();',
        );
        buffer.writeln('    }');
      }
    }

    // Make HTTP request with error handling
    buffer.writeln('    try {');
    final httpMethod = operation.httpMethod.toLowerCase();
    buffer.write('      final response = await _dio.$httpMethod(path');

    // Add query parameters if present
    if (queryParams.isNotEmpty) {
      buffer.write(', queryParameters: queryParameters');
    }

    // Add request body if present
    if (requestBodyParam != null) {
      final requestBody = operation.operation.requestBody;
      final isRequired = requestBody?.required == true;
      if (isRequired) {
        buffer.write(', data: $requestBodyParam.toJson()');
      } else {
        buffer.write(', data: $requestBodyParam?.toJson()');
      }
    }

    // Add headers if present
    if (headerParams.isNotEmpty) {
      buffer.write(', options: Options(headers: headers)');
    }

    buffer.writeln(');');

    // Handle response deserialization
    final successResponse = _getSuccessResponse(operation.operation);
    if (successResponse != null) {
      final jsonContent = successResponse.content?['application/json'];
      if (jsonContent?.schema != null) {
        final schema = jsonContent!.schema!;
        _generateResponseDeserialization(buffer, schema, '      ');
      } else {
        buffer.writeln('      return response;');
      }
    } else {
      buffer.writeln('      return response;');
    }

    // Add error handling
    buffer.writeln('    } on DioException catch (e) {');
    buffer.writeln('      throw ApiExceptionHandler.fromDioException(e);');
    buffer.writeln('    } catch (e) {');
    buffer.writeln(
      '      throw NetworkException(message: \'Unexpected error: \$e\');',
    );
    buffer.writeln('    }');
  }

  /// Extract all tags from the OpenAPI specification
  Set<String> _extractTags(OpenApiSpec spec) {
    final tags = <String>{};

    // Add tags from spec.tags
    if (spec.tags != null) {
      tags.addAll(spec.tags!.map((tag) => tag.name));
    }

    // Add tags from operations
    for (final pathItem in spec.paths.values) {
      for (final operation in pathItem.operations.values) {
        if (operation.tags != null) {
          tags.addAll(operation.tags!);
        }
      }
    }

    // If no tags found, use 'default'
    if (tags.isEmpty) {
      tags.add('default');
    }

    return tags;
  }

  /// Get all operations for a specific tag
  List<OperationInfo> _getOperationsForTag(OpenApiSpec spec, String tag) {
    final operations = <OperationInfo>[];

    for (final pathEntry in spec.paths.entries) {
      final path = pathEntry.key;
      final pathItem = pathEntry.value;

      for (final operationEntry in pathItem.operations.entries) {
        final httpMethod = operationEntry.key;
        final operation = operationEntry.value;

        // Check if operation belongs to this tag
        final operationTags = operation.tags ?? ['default'];
        if (operationTags.contains(tag)) {
          operations.add(
            OperationInfo(
              path: path,
              httpMethod: httpMethod,
              operation: operation,
            ),
          );
        }
      }
    }

    return operations;
  }

  /// Check if operation has typed response (not just dynamic)
  bool _hasTypedResponse(Operation operation) {
    // Check if any response has a schema reference or complex type
    for (final response in operation.responses.values) {
      final jsonContent = response.content?['application/json'];
      if (jsonContent?.schema != null) {
        final schema = jsonContent!.schema!;
        if (schema.ref != null ||
            schema.type == 'object' ||
            schema.type == 'array') {
          return true;
        }
      }
    }
    return false;
  }
}

/// Information about an operation
class OperationInfo {
  final String path;
  final String httpMethod;
  final Operation operation;

  const OperationInfo({
    required this.path,
    required this.httpMethod,
    required this.operation,
  });
}
