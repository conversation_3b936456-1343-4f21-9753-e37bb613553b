import 'dart:io';
import 'package:path/path.dart' as path;
import '../models/openapi_models.dart';
import 'code_generator.dart';

/// Generator for Dart model classes from OpenAPI schemas
class ModelGenerator {
  /// Generate all model classes from schemas
  Future<void> generateModels(
    Map<String, Schema> schemas,
    String outputPath,
  ) async {
    final outputDir = Directory(outputPath);
    if (!outputDir.existsSync()) {
      await outputDir.create(recursive: true);
    }

    // Generate each model class
    for (final entry in schemas.entries) {
      final schemaName = entry.key;
      final schema = entry.value;

      await _generateModel(schemaName, schema, outputPath);
    }

    // Generate barrel file for models
    await _generateModelsBarrelFile(schemas.keys, outputPath);
  }

  /// Generate a single model class
  Future<void> _generateModel(
    String schemaName,
    Schema schema,
    String outputPath,
  ) async {
    final className = CodeGenUtils.toPascalCase(schemaName);
    final fileName = CodeGenUtils.toSnakeCase(schemaName);
    final filePath = path.join(outputPath, '$fileName.dart');

    final buffer = StringBuffer();

    // File header
    buffer.writeln('// Generated model class for $schemaName');
    buffer.writeln('// This file is auto-generated. Do not edit manually.');
    buffer.writeln();

    // Add import for other models if this model references other models
    if (_hasSchemaReferences(schema)) {
      buffer.writeln("import 'models.dart';");
      buffer.writeln();
    }

    // Generate class documentation
    if (schema.description != null) {
      buffer.write(CodeGenUtils.generateDocComment(schema.description));
    }

    // Generate class declaration
    buffer.writeln('class $className {');

    // Generate properties
    final properties = schema.properties ?? <String, Schema>{};
    final requiredFields = schema.required ?? <String>[];

    for (final entry in properties.entries) {
      final propertyName = entry.key;
      final propertySchema = entry.value;
      final isRequired = requiredFields.contains(propertyName);

      _generateProperty(buffer, propertyName, propertySchema, isRequired);
    }

    // Generate constructor
    _generateConstructor(buffer, className, properties, requiredFields);

    // Generate fromJson factory constructor
    _generateFromJson(buffer, className, properties, requiredFields);

    // Generate toJson method
    _generateToJson(buffer, properties, requiredFields);

    // Generate toString method
    _generateToString(buffer, className, properties);

    // Generate equality operators
    _generateEquality(buffer, className, properties);

    buffer.writeln('}');

    // Write to file
    final file = File(filePath);
    await file.writeAsString(buffer.toString());
  }

  /// Generate property declaration
  void _generateProperty(
    StringBuffer buffer,
    String propertyName,
    Schema propertySchema,
    bool isRequired,
  ) {
    final dartPropertyName = CodeGenUtils.toCamelCase(propertyName);
    final dartType = _getDartType(propertySchema, isRequired);

    // Property documentation
    if (propertySchema.description != null) {
      buffer.write(
        CodeGenUtils.generateDocComment(propertySchema.description, indent: 2),
      );
    }

    // Property declaration
    buffer.writeln('  final $dartType $dartPropertyName;');
    buffer.writeln();
  }

  /// Generate constructor
  void _generateConstructor(
    StringBuffer buffer,
    String className,
    Map<String, Schema> properties,
    List<String> requiredFields,
  ) {
    buffer.writeln('  const $className({');

    for (final entry in properties.entries) {
      final propertyName = entry.key;
      final dartPropertyName = CodeGenUtils.toCamelCase(propertyName);
      final isRequired = requiredFields.contains(propertyName);

      if (isRequired) {
        buffer.writeln('    required this.$dartPropertyName,');
      } else {
        buffer.writeln('    this.$dartPropertyName,');
      }
    }

    buffer.writeln('  });');
    buffer.writeln();
  }

  /// Generate fromJson factory constructor
  void _generateFromJson(
    StringBuffer buffer,
    String className,
    Map<String, Schema> properties,
    List<String> requiredFields,
  ) {
    buffer.writeln(
      '  factory $className.fromJson(Map<String, dynamic> json) {',
    );
    buffer.writeln('    return $className(');

    for (final entry in properties.entries) {
      final propertyName = entry.key;
      final propertySchema = entry.value;
      final dartPropertyName = CodeGenUtils.toCamelCase(propertyName);
      final isRequired = requiredFields.contains(propertyName);

      final conversion = _generateJsonConversion(
        propertyName,
        propertySchema,
        'json',
        isRequired,
      );
      buffer.writeln('      $dartPropertyName: $conversion,');
    }

    buffer.writeln('    );');
    buffer.writeln('  }');
    buffer.writeln();
  }

  /// Generate toJson method
  void _generateToJson(
    StringBuffer buffer,
    Map<String, Schema> properties,
    List<String> requiredFields,
  ) {
    buffer.writeln('  Map<String, dynamic> toJson() {');
    buffer.writeln('    return {');

    for (final entry in properties.entries) {
      final propertyName = entry.key;
      final propertySchema = entry.value;
      final dartPropertyName = CodeGenUtils.toCamelCase(propertyName);
      final isRequired = requiredFields.contains(propertyName);

      final serialization = _generateJsonSerialization(
        dartPropertyName,
        propertySchema,
        isRequired,
      );
      buffer.writeln("      '$propertyName': $serialization,");
    }

    buffer.writeln('    };');
    buffer.writeln('  }');
    buffer.writeln();
  }

  /// Generate toString method
  void _generateToString(
    StringBuffer buffer,
    String className,
    Map<String, Schema> properties,
  ) {
    buffer.writeln('  @override');
    buffer.writeln('  String toString() {');

    if (properties.isEmpty) {
      buffer.writeln("    return '$className()';");
    } else {
      buffer.write("    return '$className(");
      final propertyStrings = properties.keys
          .map((prop) {
            final dartProp = CodeGenUtils.toCamelCase(prop);
            return '$prop: \$$dartProp';
          })
          .join(', ');
      buffer.writeln("$propertyStrings)';");
    }

    buffer.writeln('  }');
    buffer.writeln();
  }

  /// Generate equality operators
  void _generateEquality(
    StringBuffer buffer,
    String className,
    Map<String, Schema> properties,
  ) {
    // hashCode
    buffer.writeln('  @override');
    buffer.writeln('  int get hashCode {');
    if (properties.isEmpty) {
      buffer.writeln('    return 0;');
    } else {
      final hashProperties = properties.keys
          .map((prop) {
            final dartProp = CodeGenUtils.toCamelCase(prop);
            return '$dartProp.hashCode';
          })
          .join(' ^ ');
      buffer.writeln('    return $hashProperties;');
    }
    buffer.writeln('  }');
    buffer.writeln();

    // operator ==
    buffer.writeln('  @override');
    buffer.writeln('  bool operator ==(Object other) {');
    buffer.writeln('    if (identical(this, other)) return true;');
    buffer.writeln('    if (other is! $className) return false;');

    if (properties.isEmpty) {
      buffer.writeln('    return true;');
    } else {
      buffer.write('    return ');
      final equalityChecks = properties.keys
          .map((prop) {
            final dartProp = CodeGenUtils.toCamelCase(prop);
            return '$dartProp == other.$dartProp';
          })
          .join(' && ');
      buffer.writeln('$equalityChecks;');
    }

    buffer.writeln('  }');
    buffer.writeln();
  }

  /// Get Dart type for schema
  String _getDartType(Schema schema, bool isRequired) {
    String baseType;

    // Handle schema references
    if (schema.ref != null) {
      // Extract the schema name from the reference
      // e.g., "#/components/schemas/LoginRequest" -> "LoginRequest"
      final refParts = schema.ref!.split('/');
      if (refParts.length >= 3 && refParts[refParts.length - 2] == 'schemas') {
        final schemaName = refParts.last;
        baseType = CodeGenUtils.toPascalCase(schemaName);
      } else {
        baseType = 'dynamic';
      }
    }
    // Handle enum types
    else if (schema.enumValues != null && schema.enumValues!.isNotEmpty) {
      // For now, treat enums as String. In a full implementation, we'd generate enum classes
      baseType = 'String';
    } else if (schema.type != null) {
      baseType = schema.dartType;

      // Handle array types
      if (schema.isArray && schema.items != null) {
        final itemType = _getDartType(
          schema.items!,
          true,
        ); // Array items are always non-nullable
        // Remove the ? from the item type for arrays since we handle nullability at the array level
        final cleanItemType = itemType.endsWith('?')
            ? itemType.substring(0, itemType.length - 1)
            : itemType;
        baseType = 'List<$cleanItemType>';
      }
    } else if (schema.anyOf != null && schema.anyOf!.isNotEmpty) {
      // Handle anyOf - check for schema references first, then primitive types
      final refSchema = schema.anyOf!.where((s) => s.ref != null).firstOrNull;
      if (refSchema != null) {
        final refParts = refSchema.ref!.split('/');
        if (refParts.length >= 3 &&
            refParts[refParts.length - 2] == 'schemas') {
          final schemaName = refParts.last;
          baseType = CodeGenUtils.toPascalCase(schemaName);
        } else {
          baseType = 'dynamic';
        }
      } else {
        // Look for primitive types, ignoring null types
        final nonNullSchemas = schema.anyOf!
            .where((s) => s.type != 'null')
            .toList();
        if (nonNullSchemas.isNotEmpty) {
          final firstNonNull = nonNullSchemas.first;
          baseType = firstNonNull.dartType;
          // If there's a null type in anyOf, the field should be nullable
          final hasNull = schema.anyOf!.any((s) => s.type == 'null');
          if (hasNull) {
            // We'll handle nullability later, just store the base type
            baseType = firstNonNull.dartType;
          }
        } else {
          baseType = 'dynamic';
        }
      }
    } else if (schema.oneOf != null && schema.oneOf!.isNotEmpty) {
      // Handle oneOf - similar to anyOf
      final refSchema = schema.oneOf!.where((s) => s.ref != null).firstOrNull;
      if (refSchema != null) {
        final refParts = refSchema.ref!.split('/');
        if (refParts.length >= 3 &&
            refParts[refParts.length - 2] == 'schemas') {
          final schemaName = refParts.last;
          baseType = CodeGenUtils.toPascalCase(schemaName);
        } else {
          baseType = 'dynamic';
        }
      } else {
        // Look for primitive types, ignoring null types
        final nonNullSchemas = schema.oneOf!
            .where((s) => s.type != 'null')
            .toList();
        if (nonNullSchemas.isNotEmpty) {
          final firstNonNull = nonNullSchemas.first;
          baseType = firstNonNull.dartType;
        } else {
          baseType = 'dynamic';
        }
      }
    } else if (schema.allOf != null && schema.allOf!.isNotEmpty) {
      // Handle allOf - for now, use the first schema with a reference or primitive type
      final refSchema = schema.allOf!.where((s) => s.ref != null).firstOrNull;
      if (refSchema != null) {
        final refParts = refSchema.ref!.split('/');
        if (refParts.length >= 3 &&
            refParts[refParts.length - 2] == 'schemas') {
          final schemaName = refParts.last;
          baseType = CodeGenUtils.toPascalCase(schemaName);
        } else {
          baseType = 'dynamic';
        }
      } else {
        // Look for primitive types
        final primitiveSchema = schema.allOf!
            .where((s) => s.type != null && s.type != 'null')
            .firstOrNull;
        if (primitiveSchema != null) {
          baseType = primitiveSchema.dartType;
        } else {
          baseType = 'dynamic';
        }
      }
    } else {
      baseType = 'dynamic';
    }

    // Handle nullable types
    final isNullable =
        !isRequired || schema.nullable == true || _hasNullType(schema);

    if (isNullable) {
      return '$baseType?';
    }

    return baseType;
  }

  /// Generate JSON conversion code
  String _generateJsonConversion(
    String jsonKey,
    Schema schema,
    String jsonVar, [
    bool isRequired = false,
  ]) {
    // Handle schema references
    if (schema.ref != null) {
      final refParts = schema.ref!.split('/');
      if (refParts.length >= 3 && refParts[refParts.length - 2] == 'schemas') {
        final schemaName = refParts.last;
        final className = CodeGenUtils.toPascalCase(schemaName);
        if (isRequired) {
          return "$className.fromJson($jsonVar['$jsonKey'] as Map<String, dynamic>? ?? {})";
        }
        return "$jsonVar['$jsonKey'] != null ? $className.fromJson($jsonVar['$jsonKey'] as Map<String, dynamic>) : null";
      }
    }

    // Handle anyOf, oneOf, allOf with references or primitive types
    if (schema.anyOf != null && schema.anyOf!.isNotEmpty) {
      final refSchema = schema.anyOf!.where((s) => s.ref != null).firstOrNull;
      if (refSchema != null) {
        final refParts = refSchema.ref!.split('/');
        if (refParts.length >= 3 &&
            refParts[refParts.length - 2] == 'schemas') {
          final schemaName = refParts.last;
          final className = CodeGenUtils.toPascalCase(schemaName);
          if (isRequired) {
            return "$className.fromJson($jsonVar['$jsonKey'] as Map<String, dynamic>? ?? {})";
          }
          return "$jsonVar['$jsonKey'] != null ? $className.fromJson($jsonVar['$jsonKey'] as Map<String, dynamic>) : null";
        }
      } else {
        // Handle primitive types in anyOf
        final nonNullSchemas = schema.anyOf!
            .where((s) => s.type != 'null')
            .toList();
        if (nonNullSchemas.isNotEmpty) {
          final firstNonNull = nonNullSchemas.first;
          final hasNull = schema.anyOf!.any((s) => s.type == 'null');
          return _generatePrimitiveConversion(
            jsonKey,
            firstNonNull,
            jsonVar,
            isRequired && !hasNull,
          );
        }
      }
    }

    if (schema.oneOf != null && schema.oneOf!.isNotEmpty) {
      final refSchema = schema.oneOf!.where((s) => s.ref != null).firstOrNull;
      if (refSchema != null) {
        final refParts = refSchema.ref!.split('/');
        if (refParts.length >= 3 &&
            refParts[refParts.length - 2] == 'schemas') {
          final schemaName = refParts.last;
          final className = CodeGenUtils.toPascalCase(schemaName);
          if (isRequired) {
            return "$className.fromJson($jsonVar['$jsonKey'] as Map<String, dynamic>? ?? {})";
          }
          return "$jsonVar['$jsonKey'] != null ? $className.fromJson($jsonVar['$jsonKey'] as Map<String, dynamic>) : null";
        }
      } else {
        // Handle primitive types in oneOf
        final nonNullSchemas = schema.oneOf!
            .where((s) => s.type != 'null')
            .toList();
        if (nonNullSchemas.isNotEmpty) {
          final firstNonNull = nonNullSchemas.first;
          final hasNull = schema.oneOf!.any((s) => s.type == 'null');
          return _generatePrimitiveConversion(
            jsonKey,
            firstNonNull,
            jsonVar,
            isRequired && !hasNull,
          );
        }
      }
    }

    if (schema.allOf != null && schema.allOf!.isNotEmpty) {
      final refSchema = schema.allOf!.where((s) => s.ref != null).firstOrNull;
      if (refSchema != null) {
        final refParts = refSchema.ref!.split('/');
        if (refParts.length >= 3 &&
            refParts[refParts.length - 2] == 'schemas') {
          final schemaName = refParts.last;
          final className = CodeGenUtils.toPascalCase(schemaName);
          if (isRequired) {
            return "$className.fromJson($jsonVar['$jsonKey'] as Map<String, dynamic>? ?? {})";
          }
          return "$jsonVar['$jsonKey'] != null ? $className.fromJson($jsonVar['$jsonKey'] as Map<String, dynamic>) : null";
        }
      } else {
        // Handle primitive types in allOf
        final primitiveSchema = schema.allOf!
            .where((s) => s.type != null && s.type != 'null')
            .firstOrNull;
        if (primitiveSchema != null) {
          final hasNull = schema.allOf!.any((s) => s.type == 'null');
          return _generatePrimitiveConversion(
            jsonKey,
            primitiveSchema,
            jsonVar,
            isRequired && !hasNull,
          );
        }
      }
    }

    if (schema.type == null) {
      return "$jsonVar['$jsonKey']";
    }

    // For required fields, provide proper fallbacks and non-nullable types where appropriate
    switch (schema.type) {
      case 'string':
        if (isRequired) {
          return "$jsonVar['$jsonKey']?.toString() ?? ''";
        }
        return "$jsonVar['$jsonKey'] as String?";
      case 'integer':
        if (isRequired) {
          return "$jsonVar['$jsonKey'] as int? ?? 0";
        }
        return "$jsonVar['$jsonKey'] as int?";
      case 'number':
        if (isRequired) {
          return "($jsonVar['$jsonKey'] as num?)?.toDouble() ?? 0.0";
        }
        return "($jsonVar['$jsonKey'] as num?)?.toDouble()";
      case 'boolean':
        if (isRequired) {
          return "$jsonVar['$jsonKey'] as bool? ?? false";
        }
        return "$jsonVar['$jsonKey'] as bool?";
      case 'array':
        if (schema.items != null) {
          // For arrays, we need to handle the item conversion differently
          final itemConversion = _generateArrayItemConversion(schema.items!);
          final conversion =
              "($jsonVar['$jsonKey'] as List<dynamic>?)?.map((item) => $itemConversion).toList()";
          if (isRequired) {
            return "$conversion ?? []";
          }
          return conversion;
        }
        if (isRequired) {
          return "$jsonVar['$jsonKey'] as List<dynamic>? ?? []";
        }
        return "$jsonVar['$jsonKey'] as List<dynamic>?";
      case 'object':
        if (isRequired) {
          return "$jsonVar['$jsonKey'] as Map<String, dynamic>? ?? {}";
        }
        return "$jsonVar['$jsonKey'] as Map<String, dynamic>?";
      default:
        return "$jsonVar['$jsonKey']";
    }
  }

  /// Generate JSON serialization code
  String _generateJsonSerialization(
    String propertyName,
    Schema schema, [
    bool isRequired = false,
  ]) {
    // Handle schema references
    if (schema.ref != null) {
      return isRequired ? '$propertyName.toJson()' : '$propertyName?.toJson()';
    }

    // Handle anyOf, oneOf, allOf with references
    if (schema.anyOf != null && schema.anyOf!.isNotEmpty) {
      final refSchema = schema.anyOf!.firstWhere(
        (s) => s.ref != null,
        orElse: () => schema.anyOf!.first,
      );
      if (refSchema.ref != null) {
        return isRequired
            ? '$propertyName.toJson()'
            : '$propertyName?.toJson()';
      }
    }

    if (schema.oneOf != null && schema.oneOf!.isNotEmpty) {
      final refSchema = schema.oneOf!.firstWhere(
        (s) => s.ref != null,
        orElse: () => schema.oneOf!.first,
      );
      if (refSchema.ref != null) {
        return isRequired
            ? '$propertyName.toJson()'
            : '$propertyName?.toJson()';
      }
    }

    if (schema.allOf != null && schema.allOf!.isNotEmpty) {
      final refSchema = schema.allOf!.firstWhere(
        (s) => s.ref != null,
        orElse: () => schema.allOf!.first,
      );
      if (refSchema.ref != null) {
        return isRequired
            ? '$propertyName.toJson()'
            : '$propertyName?.toJson()';
      }
    }

    if (schema.type == 'array' && schema.items != null) {
      final mapExpression = _generateItemSerialization('item', schema.items!);
      if (isRequired) {
        return '$propertyName.map((item) => $mapExpression).toList()';
      } else {
        return '$propertyName?.map((item) => $mapExpression).toList()';
      }
    }

    return propertyName;
  }

  /// Generate item serialization for arrays
  String _generateItemSerialization(String itemName, Schema itemSchema) {
    // Handle schema references
    if (itemSchema.ref != null) {
      return '$itemName.toJson()';
    }

    if (itemSchema.type == 'object' || itemSchema.properties != null) {
      return '$itemName.toJson()';
    }
    return itemName;
  }

  /// Generate barrel file for all models
  Future<void> _generateModelsBarrelFile(
    Iterable<String> schemaNames,
    String outputPath,
  ) async {
    final barrelFile = File(path.join(outputPath, 'models.dart'));
    final buffer = StringBuffer();

    buffer.writeln('// Generated barrel file for all models');
    buffer.writeln('// This file exports all generated model classes');
    buffer.writeln();

    for (final schemaName in schemaNames) {
      final fileName = CodeGenUtils.toSnakeCase(schemaName);
      buffer.writeln("export '$fileName.dart';");
    }

    await barrelFile.writeAsString(buffer.toString());
  }

  /// Check if a schema has references to other schemas
  bool _hasSchemaReferences(Schema schema) {
    // Check direct reference
    if (schema.ref != null) return true;

    // Check properties for references
    if (schema.properties != null) {
      for (final property in schema.properties!.values) {
        if (_hasSchemaReferences(property)) return true;
      }
    }

    // Check array items for references
    if (schema.items != null && _hasSchemaReferences(schema.items!)) {
      return true;
    }

    // Check anyOf, oneOf, allOf for references
    if (schema.anyOf != null) {
      for (final subSchema in schema.anyOf!) {
        if (_hasSchemaReferences(subSchema)) return true;
      }
    }

    if (schema.oneOf != null) {
      for (final subSchema in schema.oneOf!) {
        if (_hasSchemaReferences(subSchema)) return true;
      }
    }

    if (schema.allOf != null) {
      for (final subSchema in schema.allOf!) {
        if (_hasSchemaReferences(subSchema)) return true;
      }
    }

    return false;
  }

  /// Generate array item conversion code
  String _generateArrayItemConversion(Schema itemSchema) {
    // Handle schema references
    if (itemSchema.ref != null) {
      final refParts = itemSchema.ref!.split('/');
      if (refParts.length >= 3 && refParts[refParts.length - 2] == 'schemas') {
        final schemaName = refParts.last;
        final className = CodeGenUtils.toPascalCase(schemaName);
        return "$className.fromJson(item as Map<String, dynamic>)";
      }
    }

    // Handle anyOf, oneOf, allOf with references
    if (itemSchema.anyOf != null && itemSchema.anyOf!.isNotEmpty) {
      final refSchema = itemSchema.anyOf!.firstWhere(
        (s) => s.ref != null,
        orElse: () => itemSchema.anyOf!.first,
      );
      if (refSchema.ref != null) {
        final refParts = refSchema.ref!.split('/');
        if (refParts.length >= 3 &&
            refParts[refParts.length - 2] == 'schemas') {
          final schemaName = refParts.last;
          final className = CodeGenUtils.toPascalCase(schemaName);
          return "$className.fromJson(item as Map<String, dynamic>)";
        }
      }
    }

    if (itemSchema.oneOf != null && itemSchema.oneOf!.isNotEmpty) {
      final refSchema = itemSchema.oneOf!.firstWhere(
        (s) => s.ref != null,
        orElse: () => itemSchema.oneOf!.first,
      );
      if (refSchema.ref != null) {
        final refParts = refSchema.ref!.split('/');
        if (refParts.length >= 3 &&
            refParts[refParts.length - 2] == 'schemas') {
          final schemaName = refParts.last;
          final className = CodeGenUtils.toPascalCase(schemaName);
          return "$className.fromJson(item as Map<String, dynamic>)";
        }
      }
    }

    if (itemSchema.allOf != null && itemSchema.allOf!.isNotEmpty) {
      final refSchema = itemSchema.allOf!.firstWhere(
        (s) => s.ref != null,
        orElse: () => itemSchema.allOf!.first,
      );
      if (refSchema.ref != null) {
        final refParts = refSchema.ref!.split('/');
        if (refParts.length >= 3 &&
            refParts[refParts.length - 2] == 'schemas') {
          final schemaName = refParts.last;
          final className = CodeGenUtils.toPascalCase(schemaName);
          return "$className.fromJson(item as Map<String, dynamic>)";
        }
      }
    }

    // Handle primitive types
    if (itemSchema.type != null) {
      switch (itemSchema.type) {
        case 'string':
          return "item?.toString() ?? ''";
        case 'integer':
          return "item as int? ?? 0";
        case 'number':
          return "(item as num?)?.toDouble() ?? 0.0";
        case 'boolean':
          return "item as bool? ?? false";
        case 'object':
          return "item as Map<String, dynamic>? ?? {}";
        default:
          return "item";
      }
    }

    return "item";
  }

  /// Check if a schema has a null type in anyOf, oneOf, or allOf
  bool _hasNullType(Schema schema) {
    // Check anyOf for null type
    if (schema.anyOf != null) {
      return schema.anyOf!.any((s) => s.type == 'null');
    }

    // Check oneOf for null type
    if (schema.oneOf != null) {
      return schema.oneOf!.any((s) => s.type == 'null');
    }

    // Check allOf for null type (less common but possible)
    if (schema.allOf != null) {
      return schema.allOf!.any((s) => s.type == 'null');
    }

    return false;
  }

  /// Generate primitive type conversion for JSON
  String _generatePrimitiveConversion(
    String jsonKey,
    Schema schema,
    String jsonVar,
    bool isRequired,
  ) {
    if (schema.type == null) {
      return "$jsonVar['$jsonKey']";
    }

    switch (schema.type) {
      case 'string':
        if (isRequired) {
          return "$jsonVar['$jsonKey']?.toString() ?? ''";
        }
        return "$jsonVar['$jsonKey'] as String?";
      case 'integer':
        if (isRequired) {
          return "$jsonVar['$jsonKey'] as int? ?? 0";
        }
        return "$jsonVar['$jsonKey'] as int?";
      case 'number':
        if (isRequired) {
          return "($jsonVar['$jsonKey'] as num?)?.toDouble() ?? 0.0";
        }
        return "($jsonVar['$jsonKey'] as num?)?.toDouble()";
      case 'boolean':
        if (isRequired) {
          return "$jsonVar['$jsonKey'] as bool? ?? false";
        }
        return "$jsonVar['$jsonKey'] as bool?";
      case 'object':
        if (isRequired) {
          return "$jsonVar['$jsonKey'] as Map<String, dynamic>? ?? {}";
        }
        return "$jsonVar['$jsonKey'] as Map<String, dynamic>?";
      default:
        return "$jsonVar['$jsonKey']";
    }
  }
}
