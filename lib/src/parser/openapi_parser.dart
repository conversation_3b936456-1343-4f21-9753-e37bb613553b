import 'dart:convert';
import 'dart:io';
import 'package:yaml/yaml.dart';
import '../models/openapi_models.dart';

/// Parser for OpenAPI specifications
class OpenApiParser {
  /// Parse OpenAPI specification from a file
  Future<OpenApiSpec> parseFromFile(String filePath) async {
    final file = File(filePath);

    if (!file.existsSync()) {
      throw OpenApiParseException('File not found: $filePath');
    }

    final content = await file.readAsString();
    final extension = filePath.toLowerCase().split('.').last;

    try {
      switch (extension) {
        case 'json':
          return parseFromJson(content);
        case 'yaml':
        case 'yml':
          return parseFromYaml(content);
        default:
          // Try to detect format by content
          return _parseFromContent(content);
      }
    } catch (e) {
      throw OpenApiParseException('Failed to parse OpenAPI specification: $e');
    }
  }

  /// Parse OpenAPI specification from JSON string
  OpenApiSpec parseFromJson(String jsonContent) {
    try {
      final json = jsonDecode(jsonContent) as Map<String, dynamic>;
      return _parseFromMap(json);
    } catch (e) {
      throw OpenApiParseException('Invalid JSON format: $e');
    }
  }

  /// Parse OpenAPI specification from YAML string
  OpenApiSpec parseFromYaml(String yamlContent) {
    try {
      final yaml = loadYaml(yamlContent);
      final map = _yamlToMap(yaml);
      return _parseFromMap(map);
    } catch (e) {
      throw OpenApiParseException('Invalid YAML format: $e');
    }
  }

  /// Parse OpenAPI specification from URL
  Future<OpenApiSpec> parseFromUrl(String url) async {
    try {
      final client = HttpClient();
      final request = await client.getUrl(Uri.parse(url));
      final response = await request.close();

      if (response.statusCode != 200) {
        throw OpenApiParseException(
          'Failed to fetch OpenAPI spec from URL: ${response.statusCode}',
        );
      }

      final content = await response.transform(utf8.decoder).join();
      client.close();

      // Determine format from URL or content-type
      final contentType = response.headers.contentType?.mimeType;
      if (contentType == 'application/json' ||
          url.toLowerCase().endsWith('.json')) {
        return parseFromJson(content);
      } else if (contentType == 'application/x-yaml' ||
          contentType == 'text/yaml' ||
          url.toLowerCase().endsWith('.yaml') ||
          url.toLowerCase().endsWith('.yml')) {
        return parseFromYaml(content);
      } else {
        return _parseFromContent(content);
      }
    } catch (e) {
      throw OpenApiParseException(
        'Failed to parse OpenAPI specification from URL: $e',
      );
    }
  }

  /// Try to parse content by detecting format
  OpenApiSpec _parseFromContent(String content) {
    // Try JSON first
    try {
      return parseFromJson(content);
    } catch (_) {
      // If JSON fails, try YAML
      try {
        return parseFromYaml(content);
      } catch (e) {
        throw OpenApiParseException(
          'Unable to parse content as JSON or YAML: $e',
        );
      }
    }
  }

  /// Parse OpenAPI specification from a Map
  OpenApiSpec _parseFromMap(Map<String, dynamic> map) {
    // Validate OpenAPI version
    final openApiVersion = map['openapi'] as String?;
    if (openApiVersion == null) {
      throw OpenApiParseException('Missing required "openapi" field');
    }

    if (!openApiVersion.startsWith('3.')) {
      throw OpenApiParseException(
        'Unsupported OpenAPI version: $openApiVersion. Only OpenAPI 3.x is supported.',
      );
    }

    // Validate required fields
    if (map['info'] == null) {
      throw OpenApiParseException('Missing required "info" field');
    }

    if (map['paths'] == null) {
      throw OpenApiParseException('Missing required "paths" field');
    }

    try {
      return OpenApiSpec.fromJson(map);
    } catch (e) {
      throw OpenApiParseException('Failed to parse OpenAPI specification: $e');
    }
  }

  /// Convert YAML to Map recursively
  Map<String, dynamic> _yamlToMap(dynamic yaml) {
    if (yaml is YamlMap) {
      return yaml.nodes.map((key, value) {
        final keyStr = key.value.toString();
        final valueConverted = _yamlToValue(value.value);
        return MapEntry(keyStr, valueConverted);
      });
    } else if (yaml is Map) {
      return yaml.map(
        (key, value) => MapEntry(key.toString(), _yamlToValue(value)),
      );
    } else {
      throw OpenApiParseException('Expected YAML map at root level');
    }
  }

  /// Convert YAML value to appropriate Dart type
  dynamic _yamlToValue(dynamic value) {
    if (value is YamlMap) {
      return _yamlToMap(value);
    } else if (value is YamlList) {
      return value.map(_yamlToValue).toList();
    } else if (value is List) {
      return value.map(_yamlToValue).toList();
    } else if (value is Map) {
      return value.map(
        (key, val) => MapEntry(key.toString(), _yamlToValue(val)),
      );
    } else {
      return value;
    }
  }

  /// Validate OpenAPI specification structure
  void validateSpec(OpenApiSpec spec) {
    final errors = <String>[];

    // Check for required info fields
    if (spec.info.title.isEmpty) {
      errors.add('Info title is required');
    }

    if (spec.info.version.isEmpty) {
      errors.add('Info version is required');
    }

    // Check paths
    if (spec.paths.isEmpty) {
      errors.add('At least one path is required');
    }

    // Validate each path
    for (final entry in spec.paths.entries) {
      final path = entry.key;
      final pathItem = entry.value;

      if (!path.startsWith('/')) {
        errors.add('Path "$path" must start with "/"');
      }

      // Check if path has at least one operation
      if (pathItem.operations.isEmpty) {
        errors.add('Path "$path" must have at least one operation');
      }
    }

    if (errors.isNotEmpty) {
      throw OpenApiValidationException(
        'OpenAPI specification validation failed:\n${errors.join('\n')}',
      );
    }
  }
}

/// Exception thrown when OpenAPI parsing fails
class OpenApiParseException implements Exception {
  final String message;

  const OpenApiParseException(this.message);

  @override
  String toString() => 'OpenApiParseException: $message';
}

/// Exception thrown when OpenAPI validation fails
class OpenApiValidationException implements Exception {
  final String message;

  const OpenApiValidationException(this.message);

  @override
  String toString() => 'OpenApiValidationException: $message';
}
