// API Exception classes for OpenAPI Dart Generator
// This file contains custom exception classes for different HTTP status codes

import 'package:dio/dio.dart';

/// Base class for all API exceptions
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final dynamic data;
  final DioException? dioException;

  const ApiException({
    required this.message,
    this.statusCode,
    this.data,
    this.dioException,
  });

  @override
  String toString() {
    return 'ApiException: $message (Status: $statusCode)';
  }
}

/// Exception for client errors (4xx status codes)
class ClientException extends ApiException {
  const ClientException({
    required super.message,
    super.statusCode,
    super.data,
    super.dioException,
  });

  @override
  String toString() {
    return 'ClientException: $message (Status: $statusCode)';
  }
}

/// Exception for server errors (5xx status codes)
class ServerException extends ApiException {
  const ServerException({
    required super.message,
    super.statusCode,
    super.data,
    super.dioException,
  });

  @override
  String toString() {
    return 'ServerException: $message (Status: $statusCode)';
  }
}

/// Exception for network/connection errors
class NetworkException extends ApiException {
  const NetworkException({required super.message, super.dioException})
    : super(statusCode: null, data: null);

  @override
  String toString() {
    return 'NetworkException: $message';
  }
}

/// Exception for timeout errors
class TimeoutException extends ApiException {
  const TimeoutException({required super.message, super.dioException})
    : super(statusCode: null, data: null);

  @override
  String toString() {
    return 'TimeoutException: $message';
  }
}

/// Exception for authentication errors (401)
class UnauthorizedException extends ClientException {
  const UnauthorizedException({
    super.message = 'Unauthorized access',
    super.statusCode = 401,
    super.data,
    super.dioException,
  });
}

/// Exception for forbidden errors (403)
class ForbiddenException extends ClientException {
  const ForbiddenException({
    super.message = 'Access forbidden',
    super.statusCode = 403,
    super.data,
    super.dioException,
  });
}

/// Exception for not found errors (404)
class NotFoundException extends ClientException {
  const NotFoundException({
    super.message = 'Resource not found',
    super.statusCode = 404,
    super.data,
    super.dioException,
  });
}

/// Exception for validation errors (422)
class ValidationException extends ClientException {
  const ValidationException({
    super.message = 'Validation failed',
    super.statusCode = 422,
    super.data,
    super.dioException,
  });
}

/// Exception for rate limiting (429)
class RateLimitException extends ClientException {
  const RateLimitException({
    super.message = 'Rate limit exceeded',
    super.statusCode = 429,
    super.data,
    super.dioException,
  });
}

/// Exception for internal server errors (500)
class InternalServerException extends ServerException {
  const InternalServerException({
    super.message = 'Internal server error',
    super.statusCode = 500,
    super.data,
    super.dioException,
  });
}

/// Exception for service unavailable (503)
class ServiceUnavailableException extends ServerException {
  const ServiceUnavailableException({
    super.message = 'Service unavailable',
    super.statusCode = 503,
    super.data,
    super.dioException,
  });
}

/// Utility class for converting DioException to ApiException
class ApiExceptionHandler {
  /// Convert DioException to appropriate ApiException
  static ApiException fromDioException(DioException dioException) {
    switch (dioException.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return TimeoutException(
          message: 'Request timeout: ${dioException.message}',
          dioException: dioException,
        );

      case DioExceptionType.connectionError:
        return NetworkException(
          message: 'Network error: ${dioException.message}',
          dioException: dioException,
        );

      case DioExceptionType.badResponse:
        final statusCode = dioException.response?.statusCode;
        final data = dioException.response?.data;
        final message =
            _extractErrorMessage(data) ?? dioException.message ?? 'HTTP error';

        return _createStatusCodeException(
          statusCode: statusCode,
          message: message,
          data: data,
          dioException: dioException,
        );

      case DioExceptionType.cancel:
        return NetworkException(
          message: 'Request cancelled',
          dioException: dioException,
        );

      case DioExceptionType.unknown:
      default:
        return NetworkException(
          message: 'Unknown error: ${dioException.message}',
          dioException: dioException,
        );
    }
  }

  /// Create appropriate exception based on status code
  static ApiException _createStatusCodeException({
    int? statusCode,
    required String message,
    dynamic data,
    DioException? dioException,
  }) {
    if (statusCode == null) {
      return NetworkException(message: message, dioException: dioException);
    }

    switch (statusCode) {
      case 401:
        return UnauthorizedException(
          message: message,
          data: data,
          dioException: dioException,
        );
      case 403:
        return ForbiddenException(
          message: message,
          data: data,
          dioException: dioException,
        );
      case 404:
        return NotFoundException(
          message: message,
          data: data,
          dioException: dioException,
        );
      case 422:
        return ValidationException(
          message: message,
          data: data,
          dioException: dioException,
        );
      case 429:
        return RateLimitException(
          message: message,
          data: data,
          dioException: dioException,
        );
      case 500:
        return InternalServerException(
          message: message,
          data: data,
          dioException: dioException,
        );
      case 503:
        return ServiceUnavailableException(
          message: message,
          data: data,
          dioException: dioException,
        );
      default:
        if (statusCode >= 400 && statusCode < 500) {
          return ClientException(
            message: message,
            statusCode: statusCode,
            data: data,
            dioException: dioException,
          );
        } else if (statusCode >= 500) {
          return ServerException(
            message: message,
            statusCode: statusCode,
            data: data,
            dioException: dioException,
          );
        } else {
          return ApiException(
            message: message,
            statusCode: statusCode,
            data: data,
            dioException: dioException,
          );
        }
    }
  }

  /// Extract error message from response data
  static String? _extractErrorMessage(dynamic data) {
    if (data is Map<String, dynamic>) {
      // Try common error message fields
      return data['message'] as String? ??
          data['error'] as String? ??
          data['detail'] as String? ??
          data['msg'] as String?;
    }
    return null;
  }
}
