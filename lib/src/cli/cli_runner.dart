import 'dart:io';
import 'package:path/path.dart' as path;
import '../parser/openapi_parser.dart';
import '../generator/code_generator.dart';

/// CLI runner that orchestrates the code generation process
class CliRunner {
  final OpenApiParser _parser = OpenApiParser();
  final CodeGenerator _generator = CodeGenerator();

  /// Generate Dart code from OpenAPI specification
  Future<void> generate({
    required String inputPath,
    required String outputPath,
    required String clientName,
    String? baseUrl,
  }) async {
    try {
      print('🚀 Starting OpenAPI Dart code generation...');

      // Validate input file
      final inputFile = File(inputPath);
      if (!inputFile.existsSync()) {
        throw Exception('Input file not found: $inputPath');
      }

      print('📖 Parsing OpenAPI specification from: $inputPath');

      // Parse OpenAPI specification
      final spec = await _parser.parseFromFile(inputPath);

      print('✅ Successfully parsed OpenAPI ${spec.openapi} specification');
      print(
        '📋 Found ${spec.paths.length} paths and ${spec.components?.schemas?.length ?? 0} schemas',
      );

      // Create output directory
      final outputDir = Directory(outputPath);
      if (!outputDir.existsSync()) {
        await outputDir.create(recursive: true);
        print('📁 Created output directory: $outputPath');
      }

      // Generate code
      print('🔧 Generating Dart code...');

      final generationConfig = GenerationConfig(
        outputPath: outputPath,
        clientName: clientName,
        baseUrl: baseUrl,
      );

      await _generator.generate(spec, generationConfig);

      print('✅ Code generation completed successfully!');
      print('📂 Generated files in: $outputPath');

      // List generated files
      await _listGeneratedFiles(outputPath);
    } catch (e, stackTrace) {
      print('❌ Error during code generation: $e');
      if (e is! Exception) {
        print('Stack trace: $stackTrace');
      }
      rethrow;
    }
  }

  /// List all generated files in the output directory
  Future<void> _listGeneratedFiles(String outputPath) async {
    final outputDir = Directory(outputPath);
    if (!outputDir.existsSync()) return;

    final files = <String>[];
    await for (final entity in outputDir.list(recursive: true)) {
      if (entity is File && entity.path.endsWith('.dart')) {
        final relativePath = path.relative(entity.path, from: outputPath);
        files.add(relativePath);
      }
    }

    if (files.isNotEmpty) {
      print('\n📄 Generated files:');
      for (final file in files) {
        print('  - $file');
      }
    }
  }
}

/// Configuration for code generation
class GenerationConfig {
  final String outputPath;
  final String clientName;
  final String? baseUrl;

  const GenerationConfig({
    required this.outputPath,
    required this.clientName,
    this.baseUrl,
  });
}
