// Extended OpenAPI model classes
// This file contains additional OpenAPI model classes

import 'openapi_models.dart';

/// OpenAPI Components object
class Components {
  final Map<String, Schema>? schemas;
  final Map<String, Response>? responses;
  final Map<String, Parameter>? parameters;
  final Map<String, Example>? examples;
  final Map<String, RequestBody>? requestBodies;
  final Map<String, Header>? headers;
  final Map<String, SecurityScheme>? securitySchemes;
  final Map<String, Link>? links;
  final Map<String, Callback>? callbacks;

  const Components({
    this.schemas,
    this.responses,
    this.parameters,
    this.examples,
    this.requestBodies,
    this.headers,
    this.securitySchemes,
    this.links,
    this.callbacks,
  });

  factory Components.fromJson(Map<String, dynamic> json) {
    return Components(
      schemas: (json['schemas'] as Map<String, dynamic>?)?.map(
        (key, value) =>
            MapEntry(key, Schema.fromJson(value as Map<String, dynamic>)),
      ),
      responses: (json['responses'] as Map<String, dynamic>?)?.map(
        (key, value) =>
            MapEntry(key, Response.fromJson(value as Map<String, dynamic>)),
      ),
      parameters: (json['parameters'] as Map<String, dynamic>?)?.map(
        (key, value) =>
            MapEntry(key, Parameter.fromJson(value as Map<String, dynamic>)),
      ),
      examples: (json['examples'] as Map<String, dynamic>?)?.map(
        (key, value) =>
            MapEntry(key, Example.fromJson(value as Map<String, dynamic>)),
      ),
      requestBodies: (json['requestBodies'] as Map<String, dynamic>?)?.map(
        (key, value) =>
            MapEntry(key, RequestBody.fromJson(value as Map<String, dynamic>)),
      ),
      headers: (json['headers'] as Map<String, dynamic>?)?.map(
        (key, value) =>
            MapEntry(key, Header.fromJson(value as Map<String, dynamic>)),
      ),
      securitySchemes: (json['securitySchemes'] as Map<String, dynamic>?)?.map(
        (key, value) => MapEntry(
          key,
          SecurityScheme.fromJson(value as Map<String, dynamic>),
        ),
      ),
      links: (json['links'] as Map<String, dynamic>?)?.map(
        (key, value) =>
            MapEntry(key, Link.fromJson(value as Map<String, dynamic>)),
      ),
      callbacks: (json['callbacks'] as Map<String, dynamic>?)?.map(
        (key, value) =>
            MapEntry(key, Callback.fromJson(value as Map<String, dynamic>)),
      ),
    );
  }
}

/// OpenAPI Response object
class Response {
  final String description;
  final Map<String, Header>? headers;
  final Map<String, MediaType>? content;
  final Map<String, Link>? links;

  const Response({
    required this.description,
    this.headers,
    this.content,
    this.links,
  });

  factory Response.fromJson(Map<String, dynamic> json) {
    return Response(
      description: json['description'] as String,
      headers: (json['headers'] as Map<String, dynamic>?)?.map(
        (key, value) =>
            MapEntry(key, Header.fromJson(value as Map<String, dynamic>)),
      ),
      content: (json['content'] as Map<String, dynamic>?)?.map(
        (key, value) =>
            MapEntry(key, MediaType.fromJson(value as Map<String, dynamic>)),
      ),
      links: (json['links'] as Map<String, dynamic>?)?.map(
        (key, value) =>
            MapEntry(key, Link.fromJson(value as Map<String, dynamic>)),
      ),
    );
  }
}

/// OpenAPI Request Body object
class RequestBody {
  final String? description;
  final Map<String, MediaType> content;
  final bool? required;

  const RequestBody({this.description, required this.content, this.required});

  factory RequestBody.fromJson(Map<String, dynamic> json) {
    return RequestBody(
      description: json['description'] as String?,
      content: (json['content'] as Map<String, dynamic>).map(
        (key, value) =>
            MapEntry(key, MediaType.fromJson(value as Map<String, dynamic>)),
      ),
      required: json['required'] as bool?,
    );
  }
}

/// OpenAPI Media Type object
class MediaType {
  final Schema? schema;
  final dynamic example;
  final Map<String, Example>? examples;
  final Map<String, Encoding>? encoding;

  const MediaType({this.schema, this.example, this.examples, this.encoding});

  factory MediaType.fromJson(Map<String, dynamic> json) {
    return MediaType(
      schema: json['schema'] != null
          ? Schema.fromJson(json['schema'] as Map<String, dynamic>)
          : null,
      example: json['example'],
      examples: (json['examples'] as Map<String, dynamic>?)?.map(
        (key, value) =>
            MapEntry(key, Example.fromJson(value as Map<String, dynamic>)),
      ),
      encoding: (json['encoding'] as Map<String, dynamic>?)?.map(
        (key, value) =>
            MapEntry(key, Encoding.fromJson(value as Map<String, dynamic>)),
      ),
    );
  }
}

/// OpenAPI Header object
class Header {
  final String? description;
  final bool? required;
  final bool? deprecated;
  final bool? allowEmptyValue;
  final String? style;
  final bool? explode;
  final bool? allowReserved;
  final Schema? schema;
  final dynamic example;
  final Map<String, Example>? examples;

  const Header({
    this.description,
    this.required,
    this.deprecated,
    this.allowEmptyValue,
    this.style,
    this.explode,
    this.allowReserved,
    this.schema,
    this.example,
    this.examples,
  });

  factory Header.fromJson(Map<String, dynamic> json) {
    return Header(
      description: json['description'] as String?,
      required: json['required'] as bool?,
      deprecated: json['deprecated'] as bool?,
      allowEmptyValue: json['allowEmptyValue'] as bool?,
      style: json['style'] as String?,
      explode: json['explode'] as bool?,
      allowReserved: json['allowReserved'] as bool?,
      schema: json['schema'] != null
          ? Schema.fromJson(json['schema'] as Map<String, dynamic>)
          : null,
      example: json['example'],
      examples: (json['examples'] as Map<String, dynamic>?)?.map(
        (key, value) =>
            MapEntry(key, Example.fromJson(value as Map<String, dynamic>)),
      ),
    );
  }
}

/// OpenAPI Example object
class Example {
  final String? summary;
  final String? description;
  final dynamic value;
  final String? externalValue;

  const Example({
    this.summary,
    this.description,
    this.value,
    this.externalValue,
  });

  factory Example.fromJson(Map<String, dynamic> json) {
    return Example(
      summary: json['summary'] as String?,
      description: json['description'] as String?,
      value: json['value'],
      externalValue: json['externalValue'] as String?,
    );
  }
}

/// OpenAPI Tag object
class Tag {
  final String name;
  final String? description;
  final ExternalDocumentation? externalDocs;

  const Tag({required this.name, this.description, this.externalDocs});

  factory Tag.fromJson(Map<String, dynamic> json) {
    return Tag(
      name: json['name'] as String,
      description: json['description'] as String?,
      externalDocs: json['externalDocs'] != null
          ? ExternalDocumentation.fromJson(
              json['externalDocs'] as Map<String, dynamic>,
            )
          : null,
    );
  }
}

/// OpenAPI External Documentation object
class ExternalDocumentation {
  final String? description;
  final String url;

  const ExternalDocumentation({this.description, required this.url});

  factory ExternalDocumentation.fromJson(Map<String, dynamic> json) {
    return ExternalDocumentation(
      description: json['description'] as String?,
      url: json['url'] as String,
    );
  }
}

/// OpenAPI Security Requirement object
class SecurityRequirement {
  final Map<String, List<String>> requirements;

  const SecurityRequirement(this.requirements);

  factory SecurityRequirement.fromJson(Map<String, dynamic> json) {
    return SecurityRequirement(
      json.map(
        (key, value) => MapEntry(key, (value as List<dynamic>).cast<String>()),
      ),
    );
  }
}

/// OpenAPI Security Scheme object
class SecurityScheme {
  final String type;
  final String? description;
  final String? name;
  final String? parameterIn;
  final String? scheme;
  final String? bearerFormat;
  final OAuthFlows? flows;
  final String? openIdConnectUrl;

  const SecurityScheme({
    required this.type,
    this.description,
    this.name,
    this.parameterIn,
    this.scheme,
    this.bearerFormat,
    this.flows,
    this.openIdConnectUrl,
  });

  factory SecurityScheme.fromJson(Map<String, dynamic> json) {
    return SecurityScheme(
      type: json['type'] as String,
      description: json['description'] as String?,
      name: json['name'] as String?,
      parameterIn: json['in'] as String?,
      scheme: json['scheme'] as String?,
      bearerFormat: json['bearerFormat'] as String?,
      flows: json['flows'] != null
          ? OAuthFlows.fromJson(json['flows'] as Map<String, dynamic>)
          : null,
      openIdConnectUrl: json['openIdConnectUrl'] as String?,
    );
  }
}

/// OpenAPI OAuth Flows object
class OAuthFlows {
  final OAuthFlow? implicit;
  final OAuthFlow? password;
  final OAuthFlow? clientCredentials;
  final OAuthFlow? authorizationCode;

  const OAuthFlows({
    this.implicit,
    this.password,
    this.clientCredentials,
    this.authorizationCode,
  });

  factory OAuthFlows.fromJson(Map<String, dynamic> json) {
    return OAuthFlows(
      implicit: json['implicit'] != null
          ? OAuthFlow.fromJson(json['implicit'] as Map<String, dynamic>)
          : null,
      password: json['password'] != null
          ? OAuthFlow.fromJson(json['password'] as Map<String, dynamic>)
          : null,
      clientCredentials: json['clientCredentials'] != null
          ? OAuthFlow.fromJson(
              json['clientCredentials'] as Map<String, dynamic>,
            )
          : null,
      authorizationCode: json['authorizationCode'] != null
          ? OAuthFlow.fromJson(
              json['authorizationCode'] as Map<String, dynamic>,
            )
          : null,
    );
  }
}

/// OpenAPI OAuth Flow object
class OAuthFlow {
  final String? authorizationUrl;
  final String? tokenUrl;
  final String? refreshUrl;
  final Map<String, String> scopes;

  const OAuthFlow({
    this.authorizationUrl,
    this.tokenUrl,
    this.refreshUrl,
    required this.scopes,
  });

  factory OAuthFlow.fromJson(Map<String, dynamic> json) {
    return OAuthFlow(
      authorizationUrl: json['authorizationUrl'] as String?,
      tokenUrl: json['tokenUrl'] as String?,
      refreshUrl: json['refreshUrl'] as String?,
      scopes: (json['scopes'] as Map<String, dynamic>).cast<String, String>(),
    );
  }
}

/// OpenAPI Callback object
class Callback {
  final Map<String, PathItem> expressions;

  const Callback(this.expressions);

  factory Callback.fromJson(Map<String, dynamic> json) {
    return Callback(
      json.map(
        (key, value) =>
            MapEntry(key, PathItem.fromJson(value as Map<String, dynamic>)),
      ),
    );
  }
}

/// OpenAPI Link object
class Link {
  final String? operationRef;
  final String? operationId;
  final Map<String, dynamic>? parameters;
  final dynamic requestBody;
  final String? description;
  final Server? server;

  const Link({
    this.operationRef,
    this.operationId,
    this.parameters,
    this.requestBody,
    this.description,
    this.server,
  });

  factory Link.fromJson(Map<String, dynamic> json) {
    return Link(
      operationRef: json['operationRef'] as String?,
      operationId: json['operationId'] as String?,
      parameters: json['parameters'] as Map<String, dynamic>?,
      requestBody: json['requestBody'],
      description: json['description'] as String?,
      server: json['server'] != null
          ? Server.fromJson(json['server'] as Map<String, dynamic>)
          : null,
    );
  }
}

/// OpenAPI Encoding object
class Encoding {
  final String? contentType;
  final Map<String, Header>? headers;
  final String? style;
  final bool? explode;
  final bool? allowReserved;

  const Encoding({
    this.contentType,
    this.headers,
    this.style,
    this.explode,
    this.allowReserved,
  });

  factory Encoding.fromJson(Map<String, dynamic> json) {
    return Encoding(
      contentType: json['contentType'] as String?,
      headers: (json['headers'] as Map<String, dynamic>?)?.map(
        (key, value) =>
            MapEntry(key, Header.fromJson(value as Map<String, dynamic>)),
      ),
      style: json['style'] as String?,
      explode: json['explode'] as bool?,
      allowReserved: json['allowReserved'] as bool?,
    );
  }
}

/// OpenAPI Discriminator object
class Discriminator {
  final String propertyName;
  final Map<String, String>? mapping;

  const Discriminator({required this.propertyName, this.mapping});

  factory Discriminator.fromJson(Map<String, dynamic> json) {
    return Discriminator(
      propertyName: json['propertyName'] as String,
      mapping: (json['mapping'] as Map<String, dynamic>?)
          ?.cast<String, String>(),
    );
  }
}

/// OpenAPI XML object
class Xml {
  final String? name;
  final String? namespace;
  final String? prefix;
  final bool? attribute;
  final bool? wrapped;

  const Xml({
    this.name,
    this.namespace,
    this.prefix,
    this.attribute,
    this.wrapped,
  });

  factory Xml.fromJson(Map<String, dynamic> json) {
    return Xml(
      name: json['name'] as String?,
      namespace: json['namespace'] as String?,
      prefix: json['prefix'] as String?,
      attribute: json['attribute'] as bool?,
      wrapped: json['wrapped'] as bool?,
    );
  }
}
