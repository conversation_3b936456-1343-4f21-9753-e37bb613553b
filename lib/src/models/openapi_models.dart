// Complete OpenAPI 3.x model definitions
// This file contains all OpenAPI model classes to avoid circular dependencies

import 'openapi_models_extended.dart';

/// Represents an OpenAPI 3.x specification
class OpenApiSpec {
  final String openapi;
  final Info info;
  final List<Server>? servers;
  final Map<String, PathItem> paths;
  final Components? components;
  final List<SecurityRequirement>? security;
  final List<Tag>? tags;
  final ExternalDocumentation? externalDocs;

  const OpenApiSpec({
    required this.openapi,
    required this.info,
    this.servers,
    required this.paths,
    this.components,
    this.security,
    this.tags,
    this.externalDocs,
  });

  factory OpenApiSpec.fromJson(Map<String, dynamic> json) {
    return OpenApiSpec(
      openapi: json['openapi'] as String,
      info: Info.fromJson(json['info'] as Map<String, dynamic>),
      servers: (json['servers'] as List<dynamic>?)
          ?.map((e) => Server.fromJson(e as Map<String, dynamic>))
          .toList(),
      paths: (json['paths'] as Map<String, dynamic>).map(
        (key, value) =>
            MapEntry(key, PathItem.fromJson(value as Map<String, dynamic>)),
      ),
      components: json['components'] != null
          ? Components.fromJson(json['components'] as Map<String, dynamic>)
          : null,
      security: (json['security'] as List<dynamic>?)
          ?.map((e) => SecurityRequirement.fromJson(e as Map<String, dynamic>))
          .toList(),
      tags: (json['tags'] as List<dynamic>?)
          ?.map((e) => Tag.fromJson(e as Map<String, dynamic>))
          .toList(),
      externalDocs: json['externalDocs'] != null
          ? ExternalDocumentation.fromJson(
              json['externalDocs'] as Map<String, dynamic>,
            )
          : null,
    );
  }
}

/// OpenAPI Info object
class Info {
  final String title;
  final String? description;
  final String? termsOfService;
  final Contact? contact;
  final License? license;
  final String version;

  const Info({
    required this.title,
    this.description,
    this.termsOfService,
    this.contact,
    this.license,
    required this.version,
  });

  factory Info.fromJson(Map<String, dynamic> json) {
    return Info(
      title: json['title'] as String,
      description: json['description'] as String?,
      termsOfService: json['termsOfService'] as String?,
      contact: json['contact'] != null
          ? Contact.fromJson(json['contact'] as Map<String, dynamic>)
          : null,
      license: json['license'] != null
          ? License.fromJson(json['license'] as Map<String, dynamic>)
          : null,
      version: json['version'] as String,
    );
  }
}

/// OpenAPI Contact object
class Contact {
  final String? name;
  final String? url;
  final String? email;

  const Contact({this.name, this.url, this.email});

  factory Contact.fromJson(Map<String, dynamic> json) {
    return Contact(
      name: json['name'] as String?,
      url: json['url'] as String?,
      email: json['email'] as String?,
    );
  }
}

/// OpenAPI License object
class License {
  final String name;
  final String? url;

  const License({required this.name, this.url});

  factory License.fromJson(Map<String, dynamic> json) {
    return License(name: json['name'] as String, url: json['url'] as String?);
  }
}

/// OpenAPI Server object
class Server {
  final String url;
  final String? description;
  final Map<String, ServerVariable>? variables;

  const Server({required this.url, this.description, this.variables});

  factory Server.fromJson(Map<String, dynamic> json) {
    return Server(
      url: json['url'] as String,
      description: json['description'] as String?,
      variables: (json['variables'] as Map<String, dynamic>?)?.map(
        (key, value) => MapEntry(
          key,
          ServerVariable.fromJson(value as Map<String, dynamic>),
        ),
      ),
    );
  }
}

/// OpenAPI Server Variable object
class ServerVariable {
  final List<String>? enumValues;
  final String defaultValue;
  final String? description;

  const ServerVariable({
    this.enumValues,
    required this.defaultValue,
    this.description,
  });

  factory ServerVariable.fromJson(Map<String, dynamic> json) {
    return ServerVariable(
      enumValues: (json['enum'] as List<dynamic>?)?.cast<String>(),
      defaultValue: json['default'] as String,
      description: json['description'] as String?,
    );
  }
}

/// OpenAPI Path Item object
class PathItem {
  final String? summary;
  final String? description;
  final Operation? get;
  final Operation? put;
  final Operation? post;
  final Operation? delete;
  final Operation? options;
  final Operation? head;
  final Operation? patch;
  final Operation? trace;
  final List<Parameter>? parameters;

  const PathItem({
    this.summary,
    this.description,
    this.get,
    this.put,
    this.post,
    this.delete,
    this.options,
    this.head,
    this.patch,
    this.trace,
    this.parameters,
  });

  factory PathItem.fromJson(Map<String, dynamic> json) {
    return PathItem(
      summary: json['summary'] as String?,
      description: json['description'] as String?,
      get: json['get'] != null
          ? Operation.fromJson(json['get'] as Map<String, dynamic>)
          : null,
      put: json['put'] != null
          ? Operation.fromJson(json['put'] as Map<String, dynamic>)
          : null,
      post: json['post'] != null
          ? Operation.fromJson(json['post'] as Map<String, dynamic>)
          : null,
      delete: json['delete'] != null
          ? Operation.fromJson(json['delete'] as Map<String, dynamic>)
          : null,
      options: json['options'] != null
          ? Operation.fromJson(json['options'] as Map<String, dynamic>)
          : null,
      head: json['head'] != null
          ? Operation.fromJson(json['head'] as Map<String, dynamic>)
          : null,
      patch: json['patch'] != null
          ? Operation.fromJson(json['patch'] as Map<String, dynamic>)
          : null,
      trace: json['trace'] != null
          ? Operation.fromJson(json['trace'] as Map<String, dynamic>)
          : null,
      parameters: (json['parameters'] as List<dynamic>?)
          ?.map((e) => Parameter.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  /// Get all operations in this path item
  Map<String, Operation> get operations {
    final ops = <String, Operation>{};
    if (get != null) ops['get'] = get!;
    if (put != null) ops['put'] = put!;
    if (post != null) ops['post'] = post!;
    if (delete != null) ops['delete'] = delete!;
    if (options != null) ops['options'] = options!;
    if (head != null) ops['head'] = head!;
    if (patch != null) ops['patch'] = patch!;
    if (trace != null) ops['trace'] = trace!;
    return ops;
  }
}

/// OpenAPI Operation object
class Operation {
  final List<String>? tags;
  final String? summary;
  final String? description;
  final ExternalDocumentation? externalDocs;
  final String? operationId;
  final List<Parameter>? parameters;
  final RequestBody? requestBody;
  final Map<String, Response> responses;
  final Map<String, Callback>? callbacks;
  final bool? deprecated;
  final List<SecurityRequirement>? security;
  final List<Server>? servers;

  const Operation({
    this.tags,
    this.summary,
    this.description,
    this.externalDocs,
    this.operationId,
    this.parameters,
    this.requestBody,
    required this.responses,
    this.callbacks,
    this.deprecated,
    this.security,
    this.servers,
  });

  factory Operation.fromJson(Map<String, dynamic> json) {
    return Operation(
      tags: (json['tags'] as List<dynamic>?)?.cast<String>(),
      summary: json['summary'] as String?,
      description: json['description'] as String?,
      externalDocs: json['externalDocs'] != null
          ? ExternalDocumentation.fromJson(
              json['externalDocs'] as Map<String, dynamic>,
            )
          : null,
      operationId: json['operationId'] as String?,
      parameters: (json['parameters'] as List<dynamic>?)
          ?.map((e) => Parameter.fromJson(e as Map<String, dynamic>))
          .toList(),
      requestBody: json['requestBody'] != null
          ? RequestBody.fromJson(json['requestBody'] as Map<String, dynamic>)
          : null,
      responses: (json['responses'] as Map<String, dynamic>).map(
        (key, value) =>
            MapEntry(key, Response.fromJson(value as Map<String, dynamic>)),
      ),
      callbacks: (json['callbacks'] as Map<String, dynamic>?)?.map(
        (key, value) =>
            MapEntry(key, Callback.fromJson(value as Map<String, dynamic>)),
      ),
      deprecated: json['deprecated'] as bool?,
      security: (json['security'] as List<dynamic>?)
          ?.map((e) => SecurityRequirement.fromJson(e as Map<String, dynamic>))
          .toList(),
      servers: (json['servers'] as List<dynamic>?)
          ?.map((e) => Server.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

/// OpenAPI Parameter object
class Parameter {
  final String name;
  final String parameterIn; // 'in' is a reserved keyword
  final String? description;
  final bool? required;
  final bool? deprecated;
  final bool? allowEmptyValue;
  final String? style;
  final bool? explode;
  final bool? allowReserved;
  final Schema? schema;
  final dynamic example;
  final Map<String, Example>? examples;

  const Parameter({
    required this.name,
    required this.parameterIn,
    this.description,
    this.required,
    this.deprecated,
    this.allowEmptyValue,
    this.style,
    this.explode,
    this.allowReserved,
    this.schema,
    this.example,
    this.examples,
  });

  factory Parameter.fromJson(Map<String, dynamic> json) {
    return Parameter(
      name: json['name'] as String,
      parameterIn: json['in'] as String,
      description: json['description'] as String?,
      required: json['required'] as bool?,
      deprecated: json['deprecated'] as bool?,
      allowEmptyValue: json['allowEmptyValue'] as bool?,
      style: json['style'] as String?,
      explode: json['explode'] as bool?,
      allowReserved: json['allowReserved'] as bool?,
      schema: json['schema'] != null
          ? Schema.fromJson(json['schema'] as Map<String, dynamic>)
          : null,
      example: json['example'],
      examples: (json['examples'] as Map<String, dynamic>?)?.map(
        (key, value) =>
            MapEntry(key, Example.fromJson(value as Map<String, dynamic>)),
      ),
    );
  }
}

/// OpenAPI Schema object
class Schema {
  final String? ref; // $ref field for schema references
  final String? title;
  final double? multipleOf;
  final double? maximum;
  final bool? exclusiveMaximum;
  final double? minimum;
  final bool? exclusiveMinimum;
  final int? maxLength;
  final int? minLength;
  final String? pattern;
  final int? maxItems;
  final int? minItems;
  final bool? uniqueItems;
  final int? maxProperties;
  final int? minProperties;
  final List<String>? required;
  final List<dynamic>? enumValues;
  final String? type;
  final List<Schema>? allOf;
  final List<Schema>? oneOf;
  final List<Schema>? anyOf;
  final Schema? not;
  final Schema? items;
  final Map<String, Schema>? properties;
  final dynamic additionalProperties;
  final String? description;
  final String? format;
  final dynamic defaultValue;
  final bool? nullable;
  final Discriminator? discriminator;
  final bool? readOnly;
  final bool? writeOnly;
  final dynamic example;
  final ExternalDocumentation? externalDocs;
  final bool? deprecated;
  final Xml? xml;

  const Schema({
    this.ref,
    this.title,
    this.multipleOf,
    this.maximum,
    this.exclusiveMaximum,
    this.minimum,
    this.exclusiveMinimum,
    this.maxLength,
    this.minLength,
    this.pattern,
    this.maxItems,
    this.minItems,
    this.uniqueItems,
    this.maxProperties,
    this.minProperties,
    this.required,
    this.enumValues,
    this.type,
    this.allOf,
    this.oneOf,
    this.anyOf,
    this.not,
    this.items,
    this.properties,
    this.additionalProperties,
    this.description,
    this.format,
    this.defaultValue,
    this.nullable,
    this.discriminator,
    this.readOnly,
    this.writeOnly,
    this.example,
    this.externalDocs,
    this.deprecated,
    this.xml,
  });

  factory Schema.fromJson(Map<String, dynamic> json) {
    return Schema(
      ref: json['\$ref'] as String?,
      title: json['title'] as String?,
      multipleOf: (json['multipleOf'] as num?)?.toDouble(),
      maximum: (json['maximum'] as num?)?.toDouble(),
      exclusiveMaximum: json['exclusiveMaximum'] as bool?,
      minimum: (json['minimum'] as num?)?.toDouble(),
      exclusiveMinimum: json['exclusiveMinimum'] as bool?,
      maxLength: json['maxLength'] as int?,
      minLength: json['minLength'] as int?,
      pattern: json['pattern'] as String?,
      maxItems: json['maxItems'] as int?,
      minItems: json['minItems'] as int?,
      uniqueItems: json['uniqueItems'] as bool?,
      maxProperties: json['maxProperties'] as int?,
      minProperties: json['minProperties'] as int?,
      required: (json['required'] as List<dynamic>?)?.cast<String>(),
      enumValues: json['enum'] as List<dynamic>?,
      type: json['type'] as String?,
      allOf: (json['allOf'] as List<dynamic>?)
          ?.map((e) => Schema.fromJson(e as Map<String, dynamic>))
          .toList(),
      oneOf: (json['oneOf'] as List<dynamic>?)
          ?.map((e) => Schema.fromJson(e as Map<String, dynamic>))
          .toList(),
      anyOf: (json['anyOf'] as List<dynamic>?)
          ?.map((e) => Schema.fromJson(e as Map<String, dynamic>))
          .toList(),
      not: json['not'] != null
          ? Schema.fromJson(json['not'] as Map<String, dynamic>)
          : null,
      items: json['items'] != null
          ? Schema.fromJson(json['items'] as Map<String, dynamic>)
          : null,
      properties: (json['properties'] as Map<String, dynamic>?)?.map(
        (key, value) =>
            MapEntry(key, Schema.fromJson(value as Map<String, dynamic>)),
      ),
      additionalProperties: json['additionalProperties'],
      description: json['description'] as String?,
      format: json['format'] as String?,
      defaultValue: json['default'],
      nullable: json['nullable'] as bool?,
      discriminator: json['discriminator'] != null
          ? Discriminator.fromJson(
              json['discriminator'] as Map<String, dynamic>,
            )
          : null,
      readOnly: json['readOnly'] as bool?,
      writeOnly: json['writeOnly'] as bool?,
      example: json['example'],
      externalDocs: json['externalDocs'] != null
          ? ExternalDocumentation.fromJson(
              json['externalDocs'] as Map<String, dynamic>,
            )
          : null,
      deprecated: json['deprecated'] as bool?,
      xml: json['xml'] != null
          ? Xml.fromJson(json['xml'] as Map<String, dynamic>)
          : null,
    );
  }

  /// Check if this schema represents an array type
  bool get isArray => type == 'array';

  /// Check if this schema represents an object type
  bool get isObject => type == 'object' || properties != null;

  /// Check if this schema represents a primitive type
  bool get isPrimitive => type != null && !isArray && !isObject;

  /// Get the Dart type name for this schema
  String get dartType {
    // Handle schema references first
    if (ref != null) {
      final refParts = ref!.split('/');
      if (refParts.length >= 3 && refParts[refParts.length - 2] == 'schemas') {
        final schemaName = refParts.last;
        return _toPascalCase(schemaName);
      }
      return 'dynamic';
    }

    // Handle enum types
    if (enumValues != null && enumValues!.isNotEmpty) {
      // For now, treat enums as String. In a full implementation, we'd generate enum classes
      return 'String';
    }

    // Handle array types
    if (type == 'array') {
      if (items != null) {
        final itemType = items!.dartType;
        return 'List<$itemType>';
      }
      return 'List<dynamic>';
    }

    // Handle composition schemas (anyOf, oneOf, allOf)
    if (anyOf != null && anyOf!.isNotEmpty) {
      return _handleCompositionSchema(anyOf!);
    }
    if (oneOf != null && oneOf!.isNotEmpty) {
      return _handleCompositionSchema(oneOf!);
    }
    if (allOf != null && allOf!.isNotEmpty) {
      return _handleCompositionSchema(allOf!);
    }

    // Handle primitive types
    switch (type) {
      case 'string':
        return 'String';
      case 'integer':
        return format == 'int64' ? 'int' : 'int';
      case 'number':
        return format == 'float' ? 'double' : 'double';
      case 'boolean':
        return 'bool';
      case 'object':
        return 'Map<String, dynamic>';
      default:
        return 'dynamic';
    }
  }

  /// Handle composition schemas (anyOf, oneOf, allOf)
  String _handleCompositionSchema(List<Schema> schemas) {
    // Look for schema references first
    final refSchema = schemas.where((s) => s.ref != null).firstOrNull;
    if (refSchema != null) {
      return refSchema.dartType;
    }

    // Look for non-null primitive types
    final nonNullSchemas = schemas.where((s) => s.type != 'null').toList();
    if (nonNullSchemas.isNotEmpty) {
      return nonNullSchemas.first.dartType;
    }

    return 'dynamic';
  }

  /// Get the Dart type name for this schema with nullability consideration
  String getDartType({bool isRequired = true}) {
    final baseType = dartType;

    // Check if the schema is nullable
    final isNullable = nullable == true || _hasNullInComposition();

    // If not required or explicitly nullable, add ? suffix
    if (!isRequired || isNullable) {
      return '$baseType?';
    }

    return baseType;
  }

  /// Check if composition schemas contain null type
  bool _hasNullInComposition() {
    if (anyOf != null && anyOf!.any((s) => s.type == 'null')) return true;
    if (oneOf != null && oneOf!.any((s) => s.type == 'null')) return true;
    if (allOf != null && allOf!.any((s) => s.type == 'null')) return true;
    return false;
  }

  /// Convert string to PascalCase (internal helper)
  String _toPascalCase(String input) {
    if (input.isEmpty) return input;

    // If the input is already in PascalCase or camelCase, preserve it
    if (RegExp(r'^[A-Z][a-zA-Z0-9]*$').hasMatch(input)) {
      return input; // Already PascalCase
    }

    if (RegExp(r'^[a-z][a-zA-Z0-9]*$').hasMatch(input)) {
      return input[0].toUpperCase() +
          input.substring(1); // Convert camelCase to PascalCase
    }

    // Handle snake_case, kebab-case, and space-separated words
    final words = input.split(RegExp(r'[_\-\s]+'));
    return words
        .map(
          (word) => word.isEmpty
              ? ''
              : word[0].toUpperCase() + word.substring(1).toLowerCase(),
        )
        .join('');
  }
}
