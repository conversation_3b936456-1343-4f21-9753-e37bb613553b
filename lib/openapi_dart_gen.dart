// OpenAPI Dart Generator
//
// A Dart package that automatically generates Dart code from OpenAPI specifications
// with Dio HTTP client integration.

// Core exports
export 'src/parser/openapi_parser.dart';
export 'src/generator/code_generator.dart';
export 'src/generator/model_generator.dart';
export 'src/generator/client_generator.dart';
export 'src/cli/cli_runner.dart';

// Model exports
// export 'src/models/openapi_models.dart';
// export 'src/models/openapi_models_extended.dart';

// Exception exports
export 'src/exceptions/api_exceptions.dart';
