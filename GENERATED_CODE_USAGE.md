# Using Generated Dart Code - OpenAPI Dart Generator

This guide shows you how to use the Dart code generated by the OpenAPI Dart Generator in your applications.

## 📋 Table of Contents

- [Quick Start](#quick-start)
- [Setting Up the API Client](#setting-up-the-api-client)
- [Making API Calls](#making-api-calls)
- [Working with Models](#working-with-models)
- [Error Handling](#error-handling)
- [Authentication](#authentication)
- [Advanced Usage](#advanced-usage)
- [Best Practices](#best-practices)

## 🚀 Quick Start

After generating code from your OpenAPI specification, you'll have a structured output with models and API clients.

### Generated Structure
```
lib/generated/
├── api.dart              # Main barrel file - import this
├── models/
│   ├── models.dart       # Models barrel file
│   ├── user.dart         # Generated model classes
│   ├── pet.dart
│   └── ...
└── client/
    ├── api_client.dart   # Main API client
    ├── users_service.dart # Service classes by tag
    ├── pets_service.dart
    └── ...
```

### Basic Setup

1. **Add dependencies to your `pubspec.yaml`:**
```yaml
dependencies:
  dio: ^5.3.0
  json_annotation: ^4.8.1

dev_dependencies:
  json_serializable: ^6.7.1
  build_runner: ^2.4.7
```

2. **Import the generated code:**
```dart
import 'package:dio/dio.dart';
import 'lib/generated/api.dart'; // Import the barrel file
```

## 🔧 Setting Up the API Client

### Basic Configuration

```dart
import 'package:dio/dio.dart';
import 'lib/generated/api.dart';

void main() async {
  // Create the API client
  final apiClient = YourApiClient(
    baseUrl: 'https://api.example.com',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    connectTimeout: Duration(seconds: 30),
    receiveTimeout: Duration(seconds: 30),
  );

  // Now you can use the client
  print('API Client ready!');
}
```

### Advanced Configuration with Custom Dio

```dart
// Create a custom Dio instance with interceptors
final dio = Dio(BaseOptions(
  baseUrl: 'https://api.example.com',
  connectTimeout: Duration(seconds: 30),
  receiveTimeout: Duration(seconds: 30),
));

// Add logging interceptor
dio.interceptors.add(LogInterceptor(
  requestBody: true,
  responseBody: true,
));

// Add authentication interceptor
dio.interceptors.add(InterceptorsWrapper(
  onRequest: (options, handler) {
    // Add auth token to every request
    options.headers['Authorization'] = 'Bearer $yourToken';
    handler.next(options);
  },
));

// Create API client with custom Dio
final apiClient = YourApiClient(dio: dio);
```

## 📞 Making API Calls

The generated code creates service classes for each tag in your OpenAPI specification.

### Basic API Calls

```dart
try {
  // GET request - fetch all users
  final usersResponse = await apiClient.usersTag.getusers();
  final users = usersResponse.data;
  
  print('Found ${users?.length} users');
  for (final user in users ?? []) {
    print('User: ${user.name} (${user.email})');
  }

  // GET request with path parameter
  final userResponse = await apiClient.usersTag.getuserbyid(123);
  final user = userResponse.data;
  print('User details: ${user?.name}');

} catch (e) {
  print('Error fetching users: $e');
}
```

### POST Requests with Request Body

```dart
try {
  // Create a new user
  final newUser = User(
    name: 'John Doe',
    email: '<EMAIL>',
    // Set other required fields
  );

  final response = await apiClient.usersTag.createuser(newUser);
  final createdUser = response.data;
  
  print('Created user with ID: ${createdUser?.id}');
} catch (e) {
  print('Error creating user: $e');
}
```

### Requests with Query Parameters

```dart
try {
  // Find pets by status
  final petsResponse = await apiClient.petTag.findpetsbystatus(
    status: 'available',
  );
  
  final availablePets = petsResponse.data;
  print('Found ${availablePets?.length} available pets');
} catch (e) {
  print('Error finding pets: $e');
}
```

## 🏗️ Working with Models

### Creating Model Instances

```dart
// Create a new model instance
final user = User(
  id: 1,
  name: 'Alice Smith',
  email: '<EMAIL>',
  // Optional fields can be omitted
);

// Models with nested objects
final pet = Pet(
  name: 'Fluffy',
  photourls: ['https://example.com/photo1.jpg'],
  category: Category(
    id: 1,
    name: 'Dogs',
  ),
  tags: [
    Tag(id: 1, name: 'friendly'),
    Tag(id: 2, name: 'playful'),
  ],
);
```

### JSON Serialization

```dart
// Convert model to JSON
final userJson = user.toJson();
print('User as JSON: $userJson');

// Create model from JSON
final jsonData = {
  'id': 1,
  'name': 'Bob Wilson',
  'email': '<EMAIL>',
};
final userFromJson = User.fromJson(jsonData);
print('User from JSON: ${userFromJson.name}');
```

### Working with Lists

```dart
// Handle list responses
final usersResponse = await apiClient.usersTag.getusers();
final users = usersResponse.data ?? [];

// Process each user
for (final user in users) {
  print('Processing user: ${user.name}');
  
  // Access optional fields safely
  if (user.email != null) {
    print('Email: ${user.email}');
  }
}

// Filter and transform
final activeUsers = users.where((user) => user.status == 'active').toList();
final userNames = users.map((user) => user.name).toList();
```

## ⚠️ Error Handling

The generated code includes comprehensive error handling with custom exceptions.

### Basic Error Handling

```dart
import 'package:openapi_dart_gen/openapi_dart_gen.dart';

try {
  final response = await apiClient.usersTag.getuserbyid(999);
  final user = response.data;
  print('User: ${user?.name}');
  
} on ApiException catch (e) {
  // Handle API-specific errors
  print('API Error: ${e.message}');
  print('Status Code: ${e.statusCode}');
  
} on NetworkException catch (e) {
  // Handle network errors
  print('Network Error: ${e.message}');
  
} on ValidationException catch (e) {
  // Handle validation errors
  print('Validation Error: ${e.message}');
  print('Field: ${e.field}');
  
} catch (e) {
  // Handle any other errors
  print('Unexpected error: $e');
}
```

### Detailed Error Handling

```dart
try {
  final response = await apiClient.usersTag.createuser(newUser);
  // Handle success
  
} on ApiException catch (e) {
  switch (e.statusCode) {
    case 400:
      print('Bad Request: Check your input data');
      break;
    case 401:
      print('Unauthorized: Please login again');
      // Redirect to login
      break;
    case 403:
      print('Forbidden: You don\'t have permission');
      break;
    case 404:
      print('Not Found: Resource doesn\'t exist');
      break;
    case 500:
      print('Server Error: Please try again later');
      break;
    default:
      print('API Error ${e.statusCode}: ${e.message}');
  }
} on DioException catch (e) {
  // Handle Dio-specific errors
  if (e.type == DioExceptionType.connectionTimeout) {
    print('Connection timeout - check your internet');
  } else if (e.type == DioExceptionType.receiveTimeout) {
    print('Server is taking too long to respond');
  }
}
```

## 🔐 Authentication

### Bearer Token Authentication

```dart
// Set authentication token
apiClient.setAuthorizationHeader('your-jwt-token');

// Make authenticated requests
final response = await apiClient.usersTag.getusers();

// Remove authentication
apiClient.removeAuthorizationHeader();
```

### Custom Authentication Headers

```dart
// Add custom headers for authentication
final dio = apiClient.dio;
dio.options.headers['X-API-Key'] = 'your-api-key';
dio.options.headers['X-Client-ID'] = 'your-client-id';
```

### Dynamic Authentication with Interceptors

```dart
apiClient.addInterceptor(InterceptorsWrapper(
  onRequest: (options, handler) async {
    // Get fresh token
    final token = await getAuthToken();
    options.headers['Authorization'] = 'Bearer $token';
    handler.next(options);
  },
  onError: (error, handler) async {
    if (error.response?.statusCode == 401) {
      // Token expired, refresh it
      final newToken = await refreshAuthToken();
      
      // Retry the request with new token
      final opts = error.requestOptions;
      opts.headers['Authorization'] = 'Bearer $newToken';
      
      final response = await apiClient.dio.fetch(opts);
      handler.resolve(response);
    } else {
      handler.next(error);
    }
  },
));
```

## 🎯 Advanced Usage

### Response Inspection

```dart
final response = await apiClient.usersTag.getusers();

// Access response metadata
print('Status Code: ${response.statusCode}');
print('Status Message: ${response.statusMessage}');
print('Headers: ${response.headers}');

// Access the actual data
final users = response.data;
print('Users count: ${users?.length}');
```

### Custom Request Options

```dart
// Override default options for specific requests
final response = await apiClient.usersTag.getusers();

// Access underlying Dio for advanced features
final dio = apiClient.dio;
final customResponse = await dio.get(
  '/custom-endpoint',
  options: Options(
    headers: {'Custom-Header': 'value'},
    receiveTimeout: Duration(seconds: 60),
  ),
);
```

### File Upload (if supported by your API)

```dart
// Upload a file (example for APIs that support file upload)
final formData = FormData.fromMap({
  'file': await MultipartFile.fromFile(
    '/path/to/file.jpg',
    filename: 'upload.jpg',
  ),
  'description': 'Profile picture',
});

final response = await apiClient.dio.post(
  '/upload',
  data: formData,
);
```

## ✅ Best Practices

### 1. Use Dependency Injection

```dart
// Create a service class
class UserService {
  final YourApiClient _apiClient;
  
  UserService(this._apiClient);
  
  Future<List<User>> getAllUsers() async {
    try {
      final response = await _apiClient.usersTag.getusers();
      return response.data ?? [];
    } catch (e) {
      // Log error and return empty list or rethrow
      print('Error fetching users: $e');
      return [];
    }
  }
}

// Use with dependency injection
final apiClient = YourApiClient(baseUrl: 'https://api.example.com');
final userService = UserService(apiClient);
```

### 2. Handle Loading States

```dart
class UserRepository {
  final YourApiClient _apiClient;
  
  UserRepository(this._apiClient);
  
  Future<Result<List<User>>> getUsers() async {
    try {
      final response = await _apiClient.usersTag.getusers();
      return Result.success(response.data ?? []);
    } on ApiException catch (e) {
      return Result.error('Failed to load users: ${e.message}');
    } catch (e) {
      return Result.error('Unexpected error occurred');
    }
  }
}

// Result wrapper class
class Result<T> {
  final T? data;
  final String? error;
  final bool isSuccess;
  
  Result.success(this.data) : error = null, isSuccess = true;
  Result.error(this.error) : data = null, isSuccess = false;
}
```

### 3. Implement Retry Logic

```dart
Future<T> withRetry<T>(Future<T> Function() operation, {int maxRetries = 3}) async {
  int attempts = 0;
  
  while (attempts < maxRetries) {
    try {
      return await operation();
    } catch (e) {
      attempts++;
      if (attempts >= maxRetries) rethrow;
      
      // Wait before retrying
      await Future.delayed(Duration(seconds: attempts));
    }
  }
  
  throw Exception('Max retries exceeded');
}

// Usage
final users = await withRetry(() async {
  final response = await apiClient.usersTag.getusers();
  return response.data ?? [];
});
```

### 4. Cache Responses

```dart
class CachedApiService {
  final YourApiClient _apiClient;
  final Map<String, dynamic> _cache = {};
  
  CachedApiService(this._apiClient);
  
  Future<List<User>> getUsers({bool forceRefresh = false}) async {
    const cacheKey = 'users';
    
    if (!forceRefresh && _cache.containsKey(cacheKey)) {
      return _cache[cacheKey] as List<User>;
    }
    
    final response = await _apiClient.usersTag.getusers();
    final users = response.data ?? [];
    
    _cache[cacheKey] = users;
    return users;
  }
}
```

## 🔍 Troubleshooting

### Common Issues

1. **Import Errors**: Make sure to import the barrel file (`api.dart`) instead of individual files
2. **Null Safety**: Always check for null values when accessing optional fields
3. **Method Names**: Generated method names are in lowercase (e.g., `getusers()` not `getUsers()`)
4. **Property Names**: Some properties may be converted to lowercase (e.g., `photourls` instead of `photoUrls`)

### Debugging Tips

```dart
// Enable Dio logging to see HTTP requests/responses
apiClient.addInterceptor(LogInterceptor(
  requestBody: true,
  responseBody: true,
  logPrint: (obj) => print(obj),
));

// Check response status and data
final response = await apiClient.usersTag.getusers();
print('Response status: ${response.statusCode}');
print('Response data type: ${response.data.runtimeType}');
print('Response data: ${response.data}');
```

---

## 📚 Next Steps

- Check the generated model classes for available properties and methods
- Explore the service classes for all available API endpoints
- Implement proper error handling and loading states in your UI
- Consider using state management solutions like Bloc, Riverpod, or Provider
- Add unit tests for your API service classes

For more advanced usage and customization options, refer to the [Dio documentation](https://pub.dev/packages/dio) and your OpenAPI specification.
